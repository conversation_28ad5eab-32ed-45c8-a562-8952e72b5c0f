package com.desaysv.workserver.action_sequence;

import cn.hutool.core.util.NumberUtil;
import com.desaysv.workserver.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 基础正则表达式规则
 */
@Slf4j
public abstract class BaseRegexRule {
    public static final String FILE_NAME_REGEX = "[\\p{Script=Han}a-zA-Z0-9_]+";
    public static final String CHECK_ALL = "all";
    public static final String NORMAL_SPLITTER = "-";
    public static final String DOT = ".";
    public static final String SPLITTER = "[-——]";
    public static final String METHOD_NAME = "[A-Za-z]\\w*";
    public static final String MESSAGE_OR_SIGNAL_NAME = "(?![0-9]+)[\\\\u4E00-\\\\u9FA5A-Za-z0-9_]+";  //禁止纯数字
    public static final String MESSAGE_OR_SIGNAL_NAME_ID = "[\\\\u4E00-\\\\u9FA5A-Za-z0-9_.{}]+";
    public static final String BYTE_X_COMMAND = "[a-zA-FX0-9 ]+";   //兼容了有空格
    public static final String NAME_VARIABLE = "[a-zA-Z0-9_!@#$%^&*(),.?\\\":{}|<>+= ]+";
    public static final String BYTE_X_COMMAND_AUTO = "[a-zA-FX0-9 ~]+";   //兼容了有空格和~
    public static final String PARENTHESIS_COMMAND = "\\(([^)]+)\\)+";
    public static final String BYTE_COMMAND = "[a-zA-F0-9 ]+";
    public static final String PARAM = "[a-zA-Z0-9]+";
    public static final String PARAM_WITH_SPACE = "[a-zA-Z0-9 ]+";
    public static final String VARIABLES_NAME = "(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]+";
    public static final String ECU_NAME = "(?!0x)[a-zA-Z0-9_]+"; //不允许匹配到0x十六进制
    public static final String NUMBER = "[0-9]*\\.?[0-9]+";
    public static final String INTEGER_NUMBER = "\\d+";
    public static final String ALL_NUMBER = "\\(\\-\\d+(\\.\\d+)?\\)|\\d+(\\.\\d+)?";
    //    public static final String ALL_NUMBER = "\\(\\-\\d+(\\.\\d+)?\\)|\\d+(\\.\\d+)?";
    public static final String ALL_NUMBER_WITH_HEX = "\\$\\-(?:0x[0-9A-Fa-f]+|\\d+(?:\\.\\d+)?)\\$|0x[0-9A-Fa-f]+|\\d+(?:\\.\\d+)?";//支持所有数字并且兼容小数的，负数带有（），0x十六进制，
    public static final String HEX_NUMBER = "0[xX][0-9a-fA-F]+";
    public static final String ALPHABET = "[a-zA-Z]+";
    public static final String WORD = "[\\p{Script=Han}\\w]+";
    public static final String WORD_BLANK = "[\\p{Script=Han}\\w\\s+]+";
    public static final String WORD_PUNCTUATION = ".+"; // 匹配任意字符
    //    public static final String CHECK_MORE_NUMBER = "\\d+(-\\d+)*";
//    public static final String CHECK_MORE_NUMBER = "(?:all)|\\d+(?:-\\d+)*";
    public static final String CHECK_MORE_NUMBER = "(?:all|(?:[0-9a-zA-Z]+(?:-[0-9a-zA-Z]+)+)|[0-9a-zA-Z]+)*";
    public static final String WORD_EXCLUDE_PURE_NUMBER = "(?!\\d+$)[\\p{Script=Han}\\w]+$"; //?!负向前瞻断言
    public static final String REGEX_BEGIN = "^";
    public static final String END_INDEX = "$";
    public static final String TIMEUNIT = "(?:ms|s|min|h)"; //?:非捕获组
    public static final String VOLTAGE_UNIT = "(?:mv|v|kv)";
    public static final String CURRENT_UNIT = "(?:mA|A|kA)";
    public static final String FREQUENCY_UNIT = "(?:Hz|KHz|MHz)";
    public static final String TIME_GROUP = wholeMatchCaseInsensitive(group(NUMBER) + group(TIMEUNIT));
    public static final String VOLTAGE_GROUP = wholeMatchCaseInsensitive(group(NUMBER) + group(VOLTAGE_UNIT));
    public static final String CURRENT_GROUP = wholeMatchCaseInsensitive(group(NUMBER) + group(CURRENT_UNIT));
    public static final String FREQUENCY_GROUP = wholeMatchCaseInsensitive(group(NUMBER) + group(FREQUENCY_UNIT));
    public static final String ANY_TIME = wholeMatchCaseInsensitive(NUMBER + optional(TIMEUNIT));
    public static final String VOLTAGE = wholeMatchCaseInsensitive(NUMBER + optional(VOLTAGE_UNIT));
    public static final String CURRENT = wholeMatchCaseInsensitive(NUMBER + optional(CURRENT_UNIT));
    public static final String FREQUENCY = wholeMatchCaseInsensitive(NUMBER + optional(FREQUENCY_UNIT));
    public static final String MILLI_TIME = add(group(ALL_NUMBER), "ms");
    public static final String EXPLAIN_KEYWORD = wholeMatchCaseInsensitive("explain");

    public static final String PTS_TX_CONSTANT = "PTSTX";
    public static final String PTX_RX_CONSTANT = "PTSRX";

    public static final String SERIAL_TX_CONSTANT = "Send";
    public static final String SERIAL_RX_CONSTANT = "Receive";
    public static final String ADB_SEND = "Send";
    public static final String TCP_SEND = "Send";
    public static final String TCP_RECEIVE = "Receive";
    public static final String EXCLUDE = wholeMatchCaseInsensitive("Exclude");
    public static final String OCR = wholeMatchCaseInsensitive("OCR");
    public static final String NUMBERSTRING = wholeMatchCaseInsensitive("NUMBER");
    public static final String NO = wholeMatchCaseInsensitive("NO");
    public static final String NA = wholeMatchCaseInsensitive("NA");
    public static final String TIME = wholeMatchCaseInsensitive("TIME");

    public final static class RegexRuleConstants {
        public static final String AMP = wholeMatchCaseInsensitive("AMP");
        public static final String REPEAT = wholeMatchCaseInsensitive("Repeat");
        public static final String CYCLE = wholeMatchCaseInsensitive("Cycle");
        public static final String CHECK = wholeMatchCaseInsensitive("Check");
        public static final String WAIT = wholeMatchCaseInsensitive("Wait");
        public static final String RANDOM = wholeMatchCaseInsensitive("Random");
        public static final String RANGE = wholeMatchCaseInsensitive("Range");
        public static final String TX = wholeMatchCaseInsensitive("TX");
        public static final String RX = wholeMatchCaseInsensitive("RX");
        public static final String SET_CURRENT = wholeMatchCaseInsensitive("SetCurrent");
        public static final String GET_CURRENT = wholeMatchCaseInsensitive("GetCurrent");
        public static final String SET_VOLTAGE = wholeMatchCaseInsensitive("SetVoltage");
        public static final String GET_VOLTAGE = wholeMatchCaseInsensitive("GetVoltage");

        public static final String SET_SIGNAL_INPUT_CONSTANTS = wholeMatchCaseInsensitive("SetSignal");
        public static final String SET_WAVEFORM_CONSTANTS = wholeMatchCaseInsensitive("WaveForm");
        public static final String SET_FREQUENCY_CONSTANTS = wholeMatchCaseInsensitive("SetFreq");
        public static final String SET_AMPLITUDE_CONSTANTS = wholeMatchCaseInsensitive("SetAMPL");
        public static final String SET_DUTY_CONSTANTS = wholeMatchCaseInsensitive("SetDuty");
        public static final String GET_FREQUENCY_CONSTANTS = wholeMatchCaseInsensitive("GetFreq");
        public static final String GET_AMPLITUDE_CONSTANTS = wholeMatchCaseInsensitive("GetAMPL");
        public static final String GET_DUTY_CONSTANTS = wholeMatchCaseInsensitive("GetDuty");
        public static final String GET_RAISE_TIME_CONSTANTS = wholeMatchCaseInsensitive("GetRaiseTime");
        public static final String GET_FULL_TIME_CONSTANTS = wholeMatchCaseInsensitive("GetFullTime");
        public static final String GET_TOP_VALUE_CONSTANTS = wholeMatchCaseInsensitive("GetTopValue");
        public static final String GET_BASE_VALUE_CONSTANTS = wholeMatchCaseInsensitive("GetBaseValue");
        public static final String MEASUREMENT = wholeMatchCaseInsensitive("measurement");
        public static final String SET_OSC_INIT = wholeMatchCaseInsensitive("SetOscInit");
        public static final String SET_OSC_STATUS = wholeMatchCaseInsensitive("SetStatus");
        public static final String AUTO_SET = wholeMatchCaseInsensitive("AutoSet");
        public static final String ALL = wholeMatchCaseInsensitive("All");
        public static final String REPEAT_LINE = add(combine(REPEAT, group(CHECK_MORE_NUMBER)));
        public static final String BEFORE_SEQUENCE_WAIT_LINE = add(combine(WAIT, group(ANY_TIME)), SPLITTER);
        public static final String BEFORE_SEQUENCE_RANDOM_WAIT_LINE = add(combine(WAIT, RegexRuleConstants.RANDOM, group(ANY_TIME), group(ANY_TIME)), SPLITTER);
        public static final String WAIT_LINE = add(combine(WAIT, group(ANY_TIME)));
        public static final String RANDOM_WAIT_LINE = add(combine(WAIT, RegexRuleConstants.RANDOM, group(ANY_TIME), group(ANY_TIME)));
        //        public static final String SINGLE_LINE_CHECK = add(SPLITTER, combine(CHECK, group(CHECK_MORE_NUMBER)));
        public static final String STEP = wholeMatchCaseInsensitive("Step");
        public static final String RAMP = wholeMatchCaseInsensitive("Ramp");
        public static final String ON_OFF = wholeMatchCaseInsensitive("(?:on|off)");
        public static final String START_STOP = wholeMatchCaseInsensitive("(?:start|stop)");
        public static final String FREQUENCY = add(group(NUMBER), caseInsensitive("hz"));
        public static final String AMPLITUDE = add(group(NUMBER), caseInsensitive("Vpp"));
        public static final String PERCENT = add(group(NUMBER), "%");
        public static final String ANY_PERCENT = add(group(ALL_NUMBER), "%");
        public static final String VOLTAGE = add(group(NUMBER), "V");
        public static final String CURRENT = add(group(NUMBER), "A");
        public static final String BYTE_LOCATION = add(caseInsensitive("byte"), group(NUMBER));
        public static final String POWER_STATE = wholeMatchCaseInsensitive("(?:on|off|acc|start)");
        public static final String INIT_STATE = wholeMatchCaseInsensitive("(?:EXCEPTKL30|ALL|1|2|3|4|5|6|7)");
        public static final String OSC_STATE = wholeMatchCaseInsensitive("(?:stop|run)");
        public static final String MIRROR_FOLD_STATUS = wholeMatchCaseInsensitive("(?:UnFlod|flod)");
        public static final String LAMP_SWITCH_STATUS = wholeMatchCaseInsensitive("(?:OFF|IHBC|AUTO|POSITION|H_LAMP|HIGHT|HIGHTOFF|FLASH|FLASHOFF|FRFOG|FRRRFOG|FOGOFF|LEFTTURN|RIGHTTURN|TURNOFF)");
        public static final String TURN_LAMP_STATUS = wholeMatchCaseInsensitive("(?:LeftTurn|RightTurn|DoubleFlash)");
        public static final String FOUR_DOOR_STATUS = wholeMatchCaseInsensitive("(?:UnLock|Lock)");
        public static final String UDS_ADDRESS_STATUS = wholeMatchCaseInsensitive("(?:Fun|Phy|0[xX][0-9a-fA-F]+)");
    }

    private static final Map<String, Float> UNIT_FACTORS = new HashMap<>();

    static {
        UNIT_FACTORS.put("mv", 0.001f);  // 毫伏 -> 伏特
        UNIT_FACTORS.put("v", 1.0f);     // 伏特
        UNIT_FACTORS.put("kv", 1000.0f); // 千伏 -> 伏特
        UNIT_FACTORS.put("ma", 0.001f);  // 毫安 -> 安培
        UNIT_FACTORS.put("a", 1.0f);     // 安培（基准单位）
        UNIT_FACTORS.put("ka", 1000.0f); // 千安 -> 安培
    }

    protected static String combine(String... rules) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < rules.length; i++) {
            if (i < rules.length - 1) {
                sb.append(rules[i]).append(SPLITTER);
            } else {
                sb.append(rules[i]);
            }
        }
        return sb.toString();
    }

    protected static String wholeCombine(String... rules) {
        return REGEX_BEGIN + combine(rules) + END_INDEX;
    }

    protected static String zeroOrMore(String word) {
        return word + "*";
    }

    /**
     * 构建一个正则表达式规则，该规则匹配“一个必须出现的模式”，
     * 后面跟着“零个或多个由指定分隔符连接的同一模式”。
     * 例如： Part + (?:Separator + Part)*
     *
     * @param requiredPart 必须出现的模式
     * @return 构建好的正则表达式字符串
     */
    protected static String oneRequiredPlusZeroOrMoreSeparated(String requiredPart) {
        // 构建重复单元: separator + requiredPart
        String repeatedUnit = add(SPLITTER, requiredPart);
        // 将重复单元包裹在非捕获分组中，然后应用 zeroOrMore 量词
        String zeroOrMoreRepeatedUnits = zeroOrMore(noGroup(repeatedUnit));
        // 将必须部分和零个或多个重复单元组合起来
        return add(requiredPart, zeroOrMoreRepeatedUnits);
    }

    protected static String add(String... rules) {
        StringBuilder finalRule = new StringBuilder();
        for (String rule : rules) {
            finalRule.append(rule);
        }
        return finalRule.toString();
    }

    protected static String lower(String word) {
        return word.toLowerCase();
    }

//    protected static String caseInsensitive(String word) {
//        String[] lowerWordList = word.toLowerCase().split("");
//        String[] upperWordList = word.toUpperCase().split("");
//        StringBuilder sb = new StringBuilder();
//        for (int pos = 0; pos < word.length(); pos++) {
//            sb.append(String.format("[%s%s]", lowerWordList[pos], upperWordList[pos]));
//        }
//        return wholeMatch(sb.toString());
//    }

    public static String wholeMatchCaseInsensitive(String word) {
        return wholeMatch(caseInsensitive(word));
    }

    protected static String caseInsensitive(String word) {
        return "(?i)" + word + "(?!\\+)";
    }

    protected static String wholeMatch(String word) {
        return noGroup("\\b" + word + "\\b");
    }

    protected static String noGroup(String word) {
        return "(?:" + word + ")";
    }

    public static String group(String word) {
        return "(" + word + ")";
    }

    public static String group(String word, String groupName) {
        return group(word);
    }

    protected static String more(String word) {
        return word + "+";
    }

    protected static String optional(String word) {
        return word + "?";
    }

    public static RegexMatcher match(String text, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        boolean match = matcher.find();
        RegexMatcher regexMatcher = new RegexMatcher();
        regexMatcher.setMatch(match);
        regexMatcher.setMatcher(matcher);
        return regexMatcher;
    }

    public static Float getSeconds(String timeWithUnit) {
        return getSeconds(timeWithUnit, "s");
    }

    public static Float getSecondsOfDefaultMills(String timeWithUnit) {
        return getSeconds(timeWithUnit, "ms");
    }

    public static Float getSeconds(String timeWithUnit, String defaultUnit) {
        if (NumberUtil.isNumber(timeWithUnit)) {
            timeWithUnit += defaultUnit;
        }
        RegexMatcher regexMatcher = BaseRegexRule.match(timeWithUnit.toLowerCase(), BaseRegexRule.TIME_GROUP);
        if (regexMatcher.isMatch()) {
            float number = Float.parseFloat(regexMatcher.getMatcher().group(1));

            String unit = regexMatcher.getMatcher().group(2);
            return DateUtils.getSeconds(number, unit);
        }
        return null;
    }

    public static void sleepSeconds(long seconds) {
        try {
            TimeUnit.SECONDS.sleep(seconds);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void sleepSeconds(float seconds) {
        try {
            long milliseconds = (long) (seconds * 1000);
            TimeUnit.MILLISECONDS.sleep(milliseconds);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
    }

    // 单位映射表（扩展维护点）
    private static final Map<String, String> UNIT_MAPPING = new HashMap<>();

    static {
        // 基础电学单位
        UNIT_MAPPING.put("ma", "mA");
        UNIT_MAPPING.put("a", "A");
        UNIT_MAPPING.put("ka", "kA");
        UNIT_MAPPING.put("mv", "mV");
        UNIT_MAPPING.put("v", "V");
        UNIT_MAPPING.put("kv", "kV");
        // 频率单位
        UNIT_MAPPING.put("hz", "Hz");
        UNIT_MAPPING.put("khz", "kHz");
        UNIT_MAPPING.put("mhz", "MHz");
        // 功率单位
        UNIT_MAPPING.put("w", "W");
        UNIT_MAPPING.put("kw", "kW");
    }


    public static float valueUnitConvert(float value, String fromUnit, String toUnit) {
        String lowerFrom = fromUnit.toLowerCase();
        String lowerTo = toUnit.toLowerCase();
        // 参数有效性校验
        if (!UNIT_FACTORS.containsKey(lowerFrom) || !UNIT_FACTORS.containsKey(lowerTo)) {
            throw new IllegalArgumentException("不支持的单位转换");
        }
        // 双阶段转换公式：value * fromFactor / toFactor
        return value * UNIT_FACTORS.get(lowerFrom) / UNIT_FACTORS.get(lowerTo);
    }


    public static float getOhms(float number, String unit) {
        float ohms = 0;
        switch (unit.toLowerCase()) {
            // 大单位转欧姆
            case "mΩ":
            case "mohm":  // 兆欧 → 欧姆
                ohms = number * 1000000.0f;
                break;
            case "kΩ":
            case "k":      // 千欧 → 欧姆
                ohms = number * 1000.0f;
                break;
            // 小单位转欧姆
            case "μΩ":
            case "u":     // 微欧 → 欧姆
                ohms = number / 1000000.0f;
                break;
            case "m":                // 毫欧 → 欧姆
                ohms = number / 1000.0f;
                break;
            // 基准单位
            case "Ω":
            case "ohm":    // 欧姆
                ohms = number;
                break;
        }
        return ohms;
    }

    public static Float getVoltage(String voltageWithUnit) {
        return getVoltage(voltageWithUnit, "v");
    }

    public static Float getVoltage(String voltageWithUnit, String defaultUnit) {
        if (NumberUtil.isNumber(voltageWithUnit)) {
            voltageWithUnit += defaultUnit;
        }
        RegexMatcher regexMatcher = BaseRegexRule.match(voltageWithUnit.toLowerCase(), BaseRegexRule.VOLTAGE_GROUP);
        if (regexMatcher.isMatch()) {
            float number = Float.parseFloat(regexMatcher.getMatcher().group(1));
            String unit = regexMatcher.getMatcher().group(2);
            return valueUnitConvert(number, unit, defaultUnit);
        }
        return -1.0f;
    }


    public static Float parseCurrent(String currentWithUnit) {
        return parseCurrent(currentWithUnit, getUnit(currentWithUnit, "A"));
    }

    public static String getUnit(String valueWithUnit, String defaultUnit) {
        return parseValueWithUnit(valueWithUnit, defaultUnit);
    }

    // 预编译正则表达式（提升性能关键[6,7,8](@ref)）
    private static final Pattern UNIT_PATTERN = Pattern.compile(
            "^\\s*(\\d+\\.?\\d*)\\s*([a-zA-Z]{1,4})\\s*$",
            Pattern.CASE_INSENSITIVE
    );

    public static String parseValueWithUnit(String valueWithUnit, String defaultUnit) {
        Matcher matcher = UNIT_PATTERN.matcher(valueWithUnit.trim());
        if (matcher.find()) {
            try {
                // 单位标准化处理（如ma→mA，hz→Hz）
                String rawUnit = matcher.group(2).toLowerCase();
                return UNIT_MAPPING.getOrDefault(rawUnit, defaultUnit);
            } catch (NumberFormatException e) {
                return defaultUnit;
            }
        }
        return defaultUnit;
    }

    public static Float parseCurrent(String currentWithUnit, String defaultUnit) {
        if (NumberUtil.isNumber(currentWithUnit)) {
            currentWithUnit += defaultUnit;
        }
        // 正则匹配示例：500mA -> (500)(ma)
        RegexMatcher matcher = BaseRegexRule.match(currentWithUnit.toLowerCase(), "(\\d+\\.?\\d*)(ma|a|ka)");
        if (matcher.isMatch()) {
            float number = Float.parseFloat(matcher.getMatcher().group(1));
            String unit = matcher.getMatcher().group(2);
            return valueUnitConvert(number, unit, defaultUnit);
        }
        return -1f;
    }
    // endregion

    // region 频率单位换算（返回基础单位Hz）
    public static float convertFrequency(float number, String unit) {
        switch (unit.toLowerCase()) {
            case "hz":
                return number;
            case "khz":
                return number * 1000.0f; // 千赫转赫兹
            case "mhz":
                return number * 1000000.0f; // 兆赫转赫兹
            default:
                throw new IllegalArgumentException("无效频率单位: " + unit);
        }
    }

    public static Float parseFrequency(String freqWithUnit) {
        return parseFrequency(freqWithUnit, "hz");
    }

    public static Float parseFrequency(String freqWithUnit, String defaultUnit) {
        if (NumberUtil.isNumber(freqWithUnit)) {
            freqWithUnit += defaultUnit;
        }
        // 正则匹配示例：2.4MHz -> (2.4)(mhz)
        RegexMatcher matcher = BaseRegexRule.match(freqWithUnit.toLowerCase(), "(\\d+\\.?\\d*)(hz|khz|mhz)");
        if (matcher.isMatch()) {
            float number = Float.parseFloat(matcher.getMatcher().group(1));
            String unit = matcher.getMatcher().group(2);
            return convertFrequency(number, unit);
        }
        return null;
    }


    public static void main(String[] args) {
        try {
            String unit = getUnit("5000mA", "V");
            System.out.println(unit);
            float getVoltage = BaseRegexRule.valueUnitConvert(4.9f, "A", unit);
            System.out.println(getVoltage);
            float voltageBaseValue = BaseRegexRule.getVoltage("5000mA", unit);
            System.out.println(voltageBaseValue);
            float voltageRange = BaseRegexRule.getVoltage("100mA", unit);
            System.out.println(voltageRange);
            boolean pass = getVoltage >= voltageBaseValue - voltageRange && getVoltage <= voltageBaseValue + voltageRange;
            System.out.println(pass);
        } catch (Exception ignored) {

        }
    }


}
