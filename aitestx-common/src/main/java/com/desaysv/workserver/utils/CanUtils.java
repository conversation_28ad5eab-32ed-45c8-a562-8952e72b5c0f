package com.desaysv.workserver.utils;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/7/21 14:49
 * @Version: 1.0
 * @Desc : 描述信息
 */
public class CanUtils {

    public static String idChange(int id, boolean isExtend) {
        if (!isExtend && id > 0x7FF) {
            throw new IllegalArgumentException("ID 超出标准帧范围，请检查输入");
        }
        if (id >= 0x1FFFFFFF) {
            throw new IllegalArgumentException("ID 超出最大允许值 0x1FFFFFFF");
        }

        String hexId = Integer.toHexString(id).toUpperCase();
        if (isExtend) {
            hexId += "x";
        }
        return hexId;
    }

}
