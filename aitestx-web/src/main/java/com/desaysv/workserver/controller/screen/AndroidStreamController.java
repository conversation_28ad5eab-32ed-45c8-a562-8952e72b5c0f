package com.desaysv.workserver.controller.screen;

import com.desaysv.workserver.devices.android.AndroidDevice;
import com.desaysv.workserver.devices.android.VideoStreamHolder;
import com.desaysv.workserver.devices.android.VideoStreamParameters;
import com.desaysv.workserver.manager.DeviceManager;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 使用NIO的非阻塞视频流控制器
 * 这是最简单有效的解决阻塞问题的方案
 */
@Slf4j
@RestController
@RequestMapping("/android/stream")
public class AndroidStreamController {

    private static final int BUFFER_SIZE = 8192;
    private static final int MAX_EMPTY_READS = 100;
    private static final long MAX_STREAM_TIME_MS = 300000; // 5分钟

    @Autowired
    private DeviceManager deviceManager;

    @GetMapping(value = "/video/{deviceId}", produces = "video/h264")
    public ResponseEntity<StreamingResponseBody> streamVideo(@PathVariable String deviceId) {

        log.info("启动设备 {} 的NIO视频流", deviceId);

        AndroidDevice device = (AndroidDevice) deviceManager.getDevice(deviceId);
        if (device == null) {
            return ResponseEntity.notFound().build();
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("video/h264"));
        headers.setCacheControl("no-cache, no-store, must-revalidate");

        // 添加设备信息到响应头
        headers.set("X-Device-Name", device.getDeviceName());
        headers.set("Connection", "keep-alive");

        StreamingResponseBody responseBody = outputStream -> {
            InputStream inputStream = null;
            ReadableByteChannel channel = null;
            AtomicBoolean streaming = new AtomicBoolean(true);

            try {
                // 启动视频流
                inputStream = device.startVideoStream();
                if (inputStream == null) {
                    throw new IOException("无法启动视频流");
                }

                // 获取实际的视频流参数
                // 记录视频流响应头信息
                log.info("视频流响应头信息:");
                log.info("  Content-Type: {}", "video/h264");
                log.info("  X-Device-Name: {}", device.getDeviceName());

                // 直接获取实际的视频流参数
                VideoStreamParameters params = device.getCurrentVideoStreamParameters();
                if (params != null) {
                    log.info("  X-Video-Resolution: {}", params.getResolution());
                    log.info("  X-Video-BitRate: {}", params.getBitRateString());
                } else {
                    log.info("  X-Video-Resolution: {}", "unknown"); // 默认分辨率
                    log.info("  X-Video-BitRate: {}", "unknown"); // 默认比特率
                }

                log.info("  Connection: {}", "keep-alive");
                log.info("  Cache-Control: {}", "no-cache, no-store, must-revalidate");

                // 转换为NIO Channel
                channel = Channels.newChannel(inputStream);
                ByteBuffer buffer = ByteBuffer.allocate(BUFFER_SIZE);

                long startTime = System.currentTimeMillis();
                int emptyReads = 0;
                long totalBytes = 0;

                log.info("设备 {} NIO视频流开始处理", deviceId);

                while (streaming.get()) {
                    // 检查超时
                    if (System.currentTimeMillis() - startTime > MAX_STREAM_TIME_MS) {
                        log.info("设备 {} 视频流达到最大时长，停止", deviceId);
                        break;
                    }

                    // 检查设备连接
                    if (!device.isConnected()) {
                        log.warn("设备 {} 连接断开，停止视频流", deviceId);
                        break;
                    }

                    try {
                        buffer.clear();
                        int bytesRead = channel.read(buffer);

                        if (bytesRead > 0) {
                            emptyReads = 0;
                            totalBytes += bytesRead;

                            // 写入数据
                            buffer.flip();
                            byte[] data = new byte[bytesRead];
                            buffer.get(data);

                            outputStream.write(data);
                            outputStream.flush();

                            // 每1MB输出一次日志
                            if (totalBytes % (1024 * 1024) == 0) {
                                log.debug("设备 {} 已传输 {} MB", deviceId, totalBytes / (1024 * 1024));
                            }

                        } else if (bytesRead == 0) {
                            // 没有数据可读，短暂等待
                            emptyReads++;
                            if (emptyReads > MAX_EMPTY_READS) {
                                log.warn("设备 {} 连续无数据读取，可能流已断开", deviceId);
                                break;
                            }
                            Thread.sleep(10); // 短暂等待
                        } else {
                            // bytesRead == -1，流结束
                            log.info("设备 {} 视频流正常结束", deviceId);
                            break;
                        }

                    } catch (IOException e) {
                        if (streaming.get()) {
                            log.warn("设备 {} 读取数据时出错: {}", deviceId, e.getMessage());
                            break;
                        }
                    }
                }

                log.info("设备 {} NIO视频流处理完成，总传输: {} 字节", deviceId, totalBytes);

            } catch (Exception e) {
                log.error("设备 {} NIO视频流异常: {}", deviceId, e.getMessage(), e);
                throw new RuntimeException("NIO视频流处理异常", e);
            } finally {
                // 清理资源
                streaming.set(false);
                if (channel != null) {
                    try {
                        channel.close();
                    } catch (IOException e) {
                        log.warn("关闭NIO通道失败: {}", e.getMessage());
                    }
                }
                if (inputStream != null) {
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        log.warn("关闭视频流失败: {}", e.getMessage());
                    }
                }
                try {
                    device.stopVideoStream();
                } catch (Exception e) {
                    log.warn("停止设备视频流失败: {}", e.getMessage());
                }
            }
        };

        return ResponseEntity.ok().headers(headers).body(responseBody);
    }

    /**
     * 最简单的直通模式 - 不处理NAL单元，直接传输原始数据
     * 这个版本最不容易阻塞
     */
    @GetMapping(value = "/raw/{deviceId}", produces = "application/octet-stream")
    public ResponseEntity<StreamingResponseBody> streamRaw(@PathVariable String deviceId) {

        log.info("启动设备 {} 的原始视频流", deviceId);

        AndroidDevice device = (AndroidDevice) deviceManager.getDevice(deviceId);
        if (device == null) {
            return ResponseEntity.notFound().build();
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setCacheControl("no-cache");

        // 添加设备信息到响应头
        headers.set("X-Device-Name", device.getDeviceName());
        headers.set("Connection", "keep-alive");

        StreamingResponseBody responseBody = outputStream -> {
            InputStream inputStream;
            try {
                inputStream = device.startVideoStream();
                if (inputStream == null) {
                    throw new IOException("无法启动视频流");
                }

                // 获取实际的视频流参数
                // 记录视频流响应头信息
                log.info("视频流响应头信息:");
                log.info("  Content-Type: {}", "application/octet-stream");
                log.info("  X-Device-Name: {}", device.getDeviceName());

                // 直接获取实际的视频流参数
                VideoStreamParameters params = device.getCurrentVideoStreamParameters();
                if (params != null) {
                    log.info("  X-Video-Resolution: {}", params.getResolution());
                    log.info("  X-Video-BitRate: {}", params.getBitRateString());
                } else {
                    log.info("  X-Video-Resolution: {}", "unknown"); // 默认分辨率
                    log.info("  X-Video-BitRate: {}", "unknown"); // 默认比特率
                }

                log.info("  Connection: {}", "keep-alive");
                log.info("  Cache-Control: {}", "no-cache");

                byte[] buffer = new byte[BUFFER_SIZE];
                int bytesRead;
                long totalBytes = 0;
                long startTime = System.currentTimeMillis();

                log.info("设备 {} 原始视频流开始传输", deviceId);

                // 直接传输，不做任何处理
                while ((bytesRead = videoStreamHolder.read(buffer)) != -1) {
                    if (bytesRead > 0) {
                        outputStream.write(buffer, 0, bytesRead);
                        outputStream.flush();
                        totalBytes += bytesRead;
                    }

                    // 检查设备连接和超时
                    if (!device.isConnected() ||
                            System.currentTimeMillis() - startTime > MAX_STREAM_TIME_MS) {
                        break;
                    }
                }

                log.info("设备 {} 原始视频流传输完成，总计: {} 字节", deviceId, totalBytes);

            } catch (Exception e) {
                log.error("设备 {} 原始视频流异常: {}", deviceId, e.getMessage());
                throw new RuntimeException("原始视频流传输异常", e);
            } finally {
                if (videoStreamHolder != null) {
                    try {
                        videoStreamHolder.close();
                    } catch (IOException e) {
                        log.warn("关闭视频流失败: {}", e.getMessage());
                    }
                }
                try {
                    device.stopVideoStream();
                } catch (Exception e) {
                    log.warn("停止设备视频流失败: {}", e.getMessage());
                }
            }
        };

        return ResponseEntity.ok().headers(headers).body(responseBody);
    }

    /**
     * 获取视频流信息的端点
     * 返回当前视频流的详细信息，包括分辨率、比特率等
     */
    @GetMapping("/info/{deviceId}")
    public ResponseEntity<VideoStreamInfo> getVideoStreamInfo(@PathVariable String deviceId) {
        try {
            AndroidDevice device = (AndroidDevice) deviceManager.getDevice(deviceId);
            if (device == null) {
                return ResponseEntity.notFound().build();
            }

            VideoStreamInfo info = new VideoStreamInfo();
            info.setDeviceName(device.getDeviceName());
            info.setDeviceId(deviceId);
            info.setConnected(device.isConnected());
            info.setStreaming(device.isStreamingVideo());

            // 获取实际的视频流参数
            VideoStreamParameters params = device.getCurrentVideoStreamParameters();
            if (params != null) {
                info.setResolution(params.getResolution());
                info.setBitRate(params.getBitRate());
                info.setContentType("video/" + params.getCodec().getCodecName());
            } else {
                // 设置默认值
                info.setResolution("unknown");
                info.setBitRate(-1);
                info.setContentType("unknown");
            }

            return ResponseEntity.ok(info);
        } catch (Exception e) {
            log.error("获取设备 {} 视频流信息失败: {}", deviceId, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }


    @PostMapping("/stop/{deviceId}")
    public ResponseEntity<String> stopStream(@PathVariable String deviceId) {
        try {
            AndroidDevice device = (AndroidDevice) deviceManager.getDevice(deviceId);
            if (device != null) {
                device.stopVideoStream();
                return ResponseEntity.ok("NIO视频流已停止");
            }
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("停止失败: " + e.getMessage());
        }
    }

    /**
     * 视频流信息类
     */
    @Setter
    @Getter
    public static class VideoStreamInfo {
        // Getters and Setters
        private String deviceName;
        private String deviceId;
        private boolean connected;
        private boolean streaming;
        private String resolution;
        private int bitRate;
        private String contentType;

    }
}

/**
 * 如果你还是遇到阻塞问题，试试这个最基础的版本
 */
//@RestController
//@RequestMapping("/android/simple")
//class SimpleStreamController {
//
//    @Autowired
//    private DeviceManager deviceManager;
//
//    @GetMapping("/video/{deviceId}")
//    public void streamVideo(@PathVariable String deviceId,
//                            javax.servlet.http.HttpServletResponse response) throws IOException {
//
//        AndroidDevice device = (AndroidDevice) deviceManager.getDevice(deviceId);
//        if (device == null) {
//            response.sendError(404, "设备未找到");
//            return;
//        }
//
//        response.setContentType("video/h264");
//        response.setHeader("Cache-Control", "no-cache");
//
//        try (InputStream videoStream = device.startVideoStream(0, 0, 2000000)) {
//            if (videoStream == null) {
//                response.sendError(500, "无法启动视频流");
//                return;
//            }
//
//            byte[] buffer = new byte[8192];
//            int bytesRead;
//
//            // 最简单的方式：直接拷贝流
//            while ((bytesRead = videoStream.read(buffer)) != -1) {
//                response.getOutputStream().write(buffer, 0, bytesRead);
//                response.getOutputStream().flush();
//
//                if (!device.isConnected()) break;
//            }
//        } catch (Exception e) {
//            log.error("简单视频流异常", e);
//        } finally {
//            device.stopVideoStream();
//        }
//    }
//}