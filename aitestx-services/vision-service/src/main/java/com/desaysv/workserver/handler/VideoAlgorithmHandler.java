package com.desaysv.workserver.handler;

import com.desaysv.workserver.algorithm.base.VisionEventHandler;
import com.desaysv.workserver.algorithm.ocr.OcrMatching;
import com.desaysv.workserver.algorithm.video.AlarmLightBlinkingDetector;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.chain.IVisionAlgorithmHandleChain;
import com.desaysv.workserver.chain.IVisionAlgorithmHandler;
import com.desaysv.workserver.constants.AlgorithmSets;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import org.bytedeco.javacv.Frame;
import org.bytedeco.opencv.opencv_core.Rect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Queue;

/**
 * 视频算法处理
 */
@Service
@Lazy
public class VideoAlgorithmHandler implements IVisionAlgorithmHandler {
    @Autowired
    private OcrMatching ocrMatching;

    @Override
    public VisionResult handleAlgorithm(VisionAlgorithm visionAlgorithm,
                                        VisionEventHandler visionEventHandler,
                                        IVisionAlgorithmHandleChain handleChain) throws OperationFailNotification {
        VisionResult visionResult = new VisionResult();
        visionResult.setTemplateName(visionAlgorithm.getTemplateName());
        // 判断传入的算法类型是否为报警灯闪烁检测
        if (visionAlgorithm.getAlgorithmName().equalsIgnoreCase(AlgorithmSets.lightBlinking)||visionAlgorithm.getAlgorithmName().equalsIgnoreCase(AlgorithmSets.ocrMatchingWithLightBlinking)) {

            // 获取之前录制的帧队列
            Queue<Frame> frameQueue = visionAlgorithm.getFrameQueue();
            if (frameQueue == null || frameQueue.isEmpty()) {
                visionResult.setPassed(false);
                visionResult.setMessage("No frames available for detection");
                return visionResult;
            }

            Rect detectionRect = visionEventHandler.getAbsoluteRoiRect(visionAlgorithm.getTemplateName());

            AlarmLightBlinkingDetector detector = new AlarmLightBlinkingDetector(frameQueue, detectionRect, visionAlgorithm.getFrameRate(), visionAlgorithm.getThreshold(), visionAlgorithm.getTargetSimilarity());
            detector.setCaptureImagePath(visionAlgorithm.getVideoFailFilePath());
            detector.setTemplateImagePath(visionAlgorithm.getVideoFailFilePath());//与模板图片路径一致
            detector.setCaptureRoiPath(visionAlgorithm.getVideoFailROIFilePath());
            // 检测
            boolean isBlinkingDetected;
            if (visionAlgorithm.getAlgorithmName().equalsIgnoreCase(AlgorithmSets.ocrMatchingWithLightBlinking)){
                isBlinkingDetected = detector.detectAndOcrMatch(visionEventHandler.getTemplateFrame(visionAlgorithm.getTemplateName()),ocrMatching,visionAlgorithm.getMatchedText());
            }else {
                isBlinkingDetected = detector.detect(visionEventHandler.getTemplateFrame(visionAlgorithm.getTemplateName()));
            }
            if (isBlinkingDetected) {
                visionResult.setPassed(true);
                visionResult.setFrequency(detector.getFrequency());
                visionResult.setMessage("指示灯闪烁");
            } else {
                visionResult.setPassed(false);
                visionResult.setMessage("指示灯静止");
                visionResult.getFileVisionResult().setCaptureImagePath(detector.getCaptureImagePath());
                visionResult.getFileVisionResult().setTemplateImagePath(detector.getTemplateImagePath());
                visionResult.getFileVisionResult().setCaptureRoiPath(detector.getCaptureRoiPath());
            }
        } else {
            // 如果不是报警灯光闪烁检测，交由下一个处理器
            visionResult = handleChain.handleAlgorithm(visionAlgorithm, visionEventHandler);
        }

        return visionResult;
    }
}
