package com.desaysv.workserver.devices.android;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.manager.AndroidImageFileManager;
import com.desaysv.workserver.manager.ImageFileManager;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.model.roi.RoiRectInfo;
import com.desaysv.workserver.model.roi.ScaledRoiRect;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ImageUtils;
import com.desaysv.workserver.utils.SpringContextHolder;
import com.desaysv.workserver.utils.command.AdbMessages;
import com.desaysv.workserver.utils.command.CommandResponse;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-21 11:05
 * @description :
 * @modified By :
 * @since : 2022-5-21
 */
@Slf4j
public class UsbAndroid extends AndroidDevice {

    private static final int DURATION_MS = 500; // 滑动持续时间，毫秒
    private static final int STEP_COUNT = 5; // 滑动步数
    private static final String MINITOUCH_PUSH = "/data/local/tmp/";
    private static final String MINITOUCH_PATH = "/data/local/tmp/minitouch";
    private static final String MINITOUCH_ARM64_v8a = "libs/stf/arm64-v8a/minitouch";
    private static final String MINITOUCH_ARMEABI_v7a = "libs/stf/armeabi-v7a/minitouch";
    private static final String MINITOUCH_X86 = "libs/stf/x86/minitouch";
    private static final String MINITOUCH_X86_64 = "libs/stf/x86_64/minitouch";

    // 截图路径，默认使用/data/local/tmp路径
    private String screenshotPath = "/data/local/tmp";

    public UsbAndroid() {
        this(new DeviceOperationParameter());
    }

    public UsbAndroid(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Android.USB_ANDROID;
    }

    @Override
    public boolean executeModel(String naturalLanguageCommand) {
        String deviceId = getDeviceName();
        return AndroidGuiAgent.execute(deviceId, naturalLanguageCommand);
    }

    @Override
    public synchronized boolean close() {
        return true;
    }


    @Override
    public String screenshot() throws OperationFailNotification {
        String targetFileName;
        String fileName = "screenshot.png";
        String fromPath = String.format("%s/%s", screenshotPath, fileName);
        File toPath = ((AndroidImageFileManager) ImageFileManager.getImageFileManager(this,
                getDeviceOperationParameter().getProject())).getScreenshotFile();
        targetFileName = new File(toPath, fileName).getAbsolutePath();
        if (isSimulated()) {
            try {
                BufferedImage bufferedImage = ImageUtils.generateRandomColorImage(1920, 1080);
                ImageIO.write(bufferedImage, "jpg", new File(targetFileName));
            } catch (IOException e) {
                throw new OperationFailNotification(e);
            }
        } else {
            try {
                String rootCommand = "adb root";
                CommandResponse adbRoot = CommandUtils.executeCommand(wrapAdbCommand(rootCommand));
                log.info(rootCommand);
                if (!adbRoot.isOk()) {
                    log.warn("{} adb root失败，原因是:{}", getDeviceName(), adbRoot.getResponse());
                    throw new OperationFailNotification(adbRoot.getResponse());
                }

                // 使用已记忆的路径
                String screenCapCommand = wrapAdbCommand(String.format("adb shell \"screencap -p > %s\"", fromPath));
                log.info(screenCapCommand);
                CommandResponse screenCapResponse = CommandUtils.executeCommand(screenCapCommand);

                // 检查是否有权限错误
                if (!screenCapResponse.isOk() && screenCapResponse.getResponse().contains("Permission denied")) {
                    // 如果有权限错误，切换到/sdcard路径
                    log.info("{}{}/{}路径权限失败，切换到/sdcard路径", getDeviceName(), screenshotPath, fileName);
                    screenshotPath = "/sdcard";
                    fromPath = String.format("%s/%s", screenshotPath, fileName);
                    screenCapCommand = wrapAdbCommand(String.format("adb shell \"screencap -p > %s\"", fromPath));
                    log.info(screenCapCommand);
                    screenCapResponse = CommandUtils.executeCommand(screenCapCommand);
                }

                if (screenCapResponse.isOk()) {
                    //screenCap截图成功
                    String screenCapPullCommand = wrapAdbCommand(String.format("adb pull %s \"%s\"", fromPath, toPath));
                    log.info(screenCapPullCommand);
                    CommandResponse screenCapPullResponse = CommandUtils.executeCommand(screenCapPullCommand);
                    if (!screenCapPullResponse.isOk()) {
                        //pull截图失败
                        log.warn("{}截图pull到电脑时失败,原因是:{}", getDeviceName(), screenCapPullResponse.getResponse());
                        throw new OperationFailNotification(screenCapPullResponse.getResponse());
                    }
                } else {
                    //screenCap截图失败
                    log.warn("{} screenCap截图失败,原因是:{}", getDeviceName(), screenCapResponse.getResponse());
                    throw new OperationFailNotification(screenCapResponse.getResponse());
                }
            } catch (IOException e) {
                throw new OperationFailNotification(e);
            }
        }
        return targetFileName;
    }

    @Override
    public boolean click(PointInt pointInt) throws OperationFailNotification {
        log.info("{}点击{}", getDeviceName(), pointInt);
        try {
            CommandResponse commandResponse = CommandUtils.executeCommand(wrapAdbCommand(String.format("adb shell input tap %d %d", pointInt.getX(), pointInt.getY())));
            if (isSimulated()) {
                return true;
            }
            if (!commandResponse.isOk()) {
                log.warn("{}点击失败,原因是:{}", getDeviceName(), commandResponse.getResponse());
                throw new OperationFailNotification(commandResponse.getResponse());
            }
        } catch (IOException e) {
            throw new OperationFailNotification(e);
        }
        return true;
    }

    @Override
    public boolean swipe(PointInt startPoint, PointInt endPoint) throws OperationFailNotification {
        //FIXME: JSONArray考虑适配List<PointInt>
        log.info("{}从{}滑动到{}", getDeviceName(), startPoint, endPoint);
        try {
            CommandResponse commandResponse = CommandUtils.executeCommand(wrapAdbCommand(String.format("adb shell input swipe %d %d %d %d",
                    startPoint.getX(), startPoint.getY(), endPoint.getX(), endPoint.getY())));
            if (!commandResponse.isOk()) {
                log.warn("{}滑动失败,原因是:{}", getDeviceName(), commandResponse.getResponse());
                throw new OperationFailNotification(commandResponse.getResponse());
            }
        } catch (IOException e) {
            throw new OperationFailNotification(e);
        }
        return true;
    }

    @Override
    public boolean testSimilarity() {
        SpringContextHolder.getBean("imageOperationCommand");
        return false;
    }

    @Override
    public boolean randomClick(RoiRectInfo roiRectInfo) throws OperationFailNotification {
        Random random = new Random();
        ScaledRoiRect roi = roiRectInfo.getRoiRect();
        RectSize pictureSize = roiRectInfo.getPictureSize();
        int xMin = (int) Math.round(roi.getPointStart().getX() * pictureSize.getWidth());
        int xMax = (int) Math.round(roi.getPointEnd().getX() * pictureSize.getWidth());
        int yMin = (int) Math.round(roi.getPointStart().getY() * pictureSize.getHeight());
        int yMax = (int) Math.round(roi.getPointEnd().getY() * pictureSize.getHeight());
        // 确保 xMin < xMax 和 yMin < yMax，如果不是则交换
        if (xMin > xMax) {
            int temp = xMin;
            xMin = xMax;
            xMax = temp;
        }

        if (yMin > yMax) {
            int temp = yMin;
            yMin = yMax;
            yMax = temp;
        }
        int x = xMin + random.nextInt(xMax - xMin + 1);
        int y = yMin + random.nextInt(yMax - yMin + 1);

        log.info("{}随机点击坐标: ({}, {})", getDeviceName(), x, y);
        return click(new PointInt(x, y));
    }

    @Override
    public boolean executeADBCommand(String command) throws OperationFailNotification {
        log.info("{}执行命令{}", getDeviceName(), command);
        try {
            CommandResponse commandResponse = CommandUtils.executeCommand(command);
            if (!commandResponse.isOk()) {
                log.warn("{}执行命令{}失败,原因是:{}", getDeviceName(), command, commandResponse.getResponse());
                throw new OperationFailNotification(commandResponse.getResponse());
            }
        } catch (IOException e) {
            throw new OperationFailNotification(e);
        }
        return true;
    }

    public boolean executeADBCommand(AdbMessages adbMessages) throws OperationFailNotification {
        log.info("{}执行命令{}", getDeviceName(), adbMessages.getAdbMessage());
        return false;
    }

    public boolean adbMustExist(String expectedAdbResult, int timeoutMillis) {
        return false;
    }

    public boolean adbForbidExist(String expectedAdbResult, int timeoutMillis) {
        return false;
    }

    @Override
    public boolean randomSwipe(RoiRectInfo roiRectInfo) throws OperationFailNotification {
        Random random = new Random();
        ScaledRoiRect roi = roiRectInfo.getRoiRect();
        RectSize pictureSize = roiRectInfo.getPictureSize();
        int xMin = (int) Math.round(roi.getPointStart().getX() * pictureSize.getWidth());
        int xMax = (int) Math.round(roi.getPointEnd().getX() * pictureSize.getWidth());
        int yMin = (int) Math.round(roi.getPointStart().getY() * pictureSize.getHeight());
        int yMax = (int) Math.round(roi.getPointEnd().getY() * pictureSize.getHeight());
        // 确保 xMin < xMax 和 yMin < yMax，如果不是则交换
        if (xMin > xMax) {
            int temp = xMin;
            xMin = xMax;
            xMax = temp;
        }

        if (yMin > yMax) {
            int temp = yMin;
            yMin = yMax;
            yMax = temp;
        }
        int startX = xMin + random.nextInt(xMax - xMin + 1);
        int startY = yMin + random.nextInt(yMax - yMin + 1);
        int endX = xMin + random.nextInt(xMax - xMin + 1);
        int endY = yMin + random.nextInt(yMax - yMin + 1);

        log.info("{}随机滑动从坐标: ({}, {}) 到坐标: ({}, {})", getDeviceName(), startX, startY, endX, endY);
        try {
            CommandResponse commandResponse = CommandUtils.executeCommand(
                    wrapAdbCommand(String.format("adb shell input swipe %d %d %d %d", startX, startY, endX, endY))
            );
            if (!commandResponse.isOk()) {
                log.warn("{}滑动失败, 原因是: {}", getDeviceName(), commandResponse.getResponse());
                throw new OperationFailNotification(commandResponse.getResponse());
            }
        } catch (IOException e) {
            throw new OperationFailNotification(e);
        }

        return true;
    }

    @Override
    public boolean multiFingerSwipe(RoiRectInfo roiRectInfo, int fingerCount, String direction) throws OperationFailNotification, IOException {
        //先判断minitouch是否已经推送
        ensureMinitouchPushed();
        // 启动 minitouch 并将其放入后台
        Runtime.getRuntime().exec("adb shell nohup /data/local/tmp/minitouch > /data/local/tmp/minitouch.log 2>&1 &");
        // 检查 minitouch 是否正在运行
        Process checkProcess = Runtime.getRuntime().exec("adb shell ps | grep minitouch");
        BufferedReader reader = new BufferedReader(new InputStreamReader(checkProcess.getInputStream()));
        String line;
        boolean isRunning = false;
        while ((line = reader.readLine()) != null) {
            log.info("minitouch is running: {}", line);
            isRunning = true;
        }
        if (!isRunning) {
            throw new RuntimeException("minitouch is not running!");
        }
        // 使用 adb forward 将本地端口 1111 转发到设备上的 minitouch 端口
        Runtime.getRuntime().exec("adb forward tcp:1111 localabstract:minitouch");

        //计算滑动坐标
        ScaledRoiRect roi = roiRectInfo.getRoiRect();
        RectSize pictureSize = roiRectInfo.getPictureSize();
        int xMin = (int) Math.round(roi.getPointStart().getX() * pictureSize.getWidth());
        int xMax = (int) Math.round(roi.getPointEnd().getX() * pictureSize.getWidth());
        int yMin = (int) Math.round(roi.getPointStart().getY() * pictureSize.getHeight());
        int yMax = (int) Math.round(roi.getPointEnd().getY() * pictureSize.getHeight());
        int partitionHeight = (yMax - yMin) / (fingerCount - 1);
        int partitionWidth = (xMax - xMin) / (fingerCount - 1);

        // 确保 xMin < xMax 和 yMin < yMax，如果不是则交换
        if (xMin > xMax) {
            int temp = xMin;
            xMin = xMax;
            xMax = temp;
        }

        if (yMin > yMax) {
            int temp = yMin;
            yMin = yMax;
            yMax = temp;
        }

        List<int[]> startPoints = new ArrayList<>();
        List<int[]> endPoints = new ArrayList<>();

        for (int i = 0; i < fingerCount; i++) {
            int startX, startY, endX, endY;

            if (direction.equals("HORIZONTAL_LEFT")) {
                startX = xMax;
                startY = yMin + i * partitionHeight;
                endX = xMin;
                endY = startY + 2;
            } else if (direction.equals("HORIZONTAL_RIGHT")) {
                startX = xMin;
                startY = yMin + i * partitionHeight;
                endX = xMax;
                endY = startY + 2;
            } else if (direction.equals("VERTICAL_UP")) {
                startX = xMin + i * partitionWidth;
                startY = yMin;
                endX = startX + 2;
                endY = yMax;
            } else if (direction.equals("VERTICAL_DOWN")) {
                startX = xMin + i * partitionWidth;
                startY = yMax;
                endX = startX + 2;
                endY = yMin;
            } else {
                throw new IllegalArgumentException("不支持方向: " + direction + "的滑动");
            }

            startPoints.add(new int[]{startX, startY});
            endPoints.add(new int[]{endX, endY});

            log.info("{}多指滑动中第{}个手指从坐标: ({}, {}) 到坐标: ({}, {})", getDeviceName(), (i + 1), startX, startY, endX, endY);
        }

        // 使用 Socket 连接到 minitouch，并发送触摸指令
        try (Socket socket = new Socket("127.0.0.1", 1111);
             OutputStream outputStream = socket.getOutputStream();
             BufferedReader inputReader = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

            String response;
            while ((response = inputReader.readLine()) != null && !response.isEmpty()) {
                log.info("minitouch info: {}", response);
                if (response.startsWith("^")) break; // Stop reading after capabilities are received
            }

            // 开始触摸并提交
            for (int i = 0; i < fingerCount; i++) {
                sendCommand(outputStream, String.format("d %d %d %d 100\n", i, startPoints.get(i)[0], startPoints.get(i)[1]));
            }
            sendCommand(outputStream, "c\n"); // 提交触摸开始
            sendCommand(outputStream, "w 50\n"); // 等待50ms让手指放在屏幕上

            // 滑动
            for (int step = 0; step <= STEP_COUNT; step++) {
                for (int i = 0; i < fingerCount; i++) {
                    int[] startPoint = startPoints.get(i);
                    int[] endPoint = endPoints.get(i);
                    int x = (int) (startPoint[0] + (endPoint[0] - startPoint[0]) * (step * 1.0 / STEP_COUNT));
                    int y = (int) (startPoint[1] + (endPoint[1] - startPoint[1]) * (step * 1.0 / STEP_COUNT));

                    sendCommand(outputStream, String.format("m %d %d %d 100\n", i, x, y));
                }
                sendCommand(outputStream, "c\n"); // 提交移动
                sendCommand(outputStream, "w " + (DURATION_MS / STEP_COUNT) + "\n");
            }

            // 结束触摸并提交
            for (int i = 0; i < fingerCount; i++) {
                sendCommand(outputStream, String.format("u %d\n", i));
            }
            sendCommand(outputStream, "c\n"); // 提交触摸结束
            sendCommand(outputStream, "r\n"); // 重置状态

        } catch (Exception e) {
            log.warn("{}多指滑动失败, 原因是: {}", getDeviceName(), e.getMessage());
            throw new OperationFailNotification(e.getMessage());
        }

        return true;
    }

    private void sendCommand(OutputStream outputStream, String command) throws Exception {
        outputStream.write((command + "\n").getBytes());  // 确保每条命令以换行符结尾
        outputStream.flush();
    }

    /**
     * 检查设备上是否已经推送了minitouch并且具有正确的权限。
     *
     * @return 如果minitouch存在且权限正确，则返回true；否则返回false。
     */
    public boolean isMinitouchPushed() {
        try {
            // 检查文件是否存在
            Process process = Runtime.getRuntime().exec("adb shell ls -l " + MINITOUCH_PATH);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                log.info("minitouch文件信息: {}", line);
                if (line.contains(MINITOUCH_PATH)) {
                    // 检查文件权限
                    if (line.startsWith("-rwxr-xr-x")) {
                        return true;
                    } else {
                        log.warn("minitouch文件权限不正确: {}", line);
                        return false;
                    }
                }
            }
            log.warn("minitouch文件不存在");
            return false;
        } catch (Exception e) {
            log.error("检查minitouch时出错: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取设备架构。
     *
     * @return 设备架构字符串，例如"arm64-v8a"或"armeabi-v7a"。
     */
    public String getDeviceArchitecture() {
        try {
            Process process = Runtime.getRuntime().exec("adb shell getprop ro.product.cpu.abi");
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String architecture = reader.readLine();
            if (architecture == null || architecture.isEmpty()) {
                throw new RuntimeException("无法获取设备架构");
            }
            log.info("{}设备架构为: {}", getDeviceName(), architecture);
            return architecture;
        } catch (Exception e) {
            log.error("获取设备架构时出错: {}", e.getMessage(), e);
            throw new RuntimeException("无法获取设备架构", e);
        }
    }

    /**
     * 推送并设置minitouch权限。
     *
     * @param architecture 设备架构。
     */
    public void pushMinitouch(String architecture) {
        String minitouchPath;
        switch (architecture) {
            case "arm64-v8a":
                minitouchPath = MINITOUCH_ARM64_v8a;
                break;
            case "armeabi-v7a":
                minitouchPath = MINITOUCH_ARMEABI_v7a;
                break;
            case "x86":
                minitouchPath = MINITOUCH_X86;
                break;
            case "x86_64":
                minitouchPath = MINITOUCH_X86_64;
                break;
            default:
                throw new RuntimeException("不支持的设备架构: " + architecture);
        }

        try {
            log.info("{}多指滑动中，开始推送适配的minitouch: {}", getDeviceName(), minitouchPath);
            Process pushProcess = Runtime.getRuntime().exec("adb push " + minitouchPath + " " + MINITOUCH_PUSH);
            int pushExitCode = pushProcess.waitFor();
            if (pushExitCode != 0) {
                throw new RuntimeException("minitouch推送失败，退出码: " + pushExitCode);
            }

            Process chmodProcess = Runtime.getRuntime().exec("adb shell chmod 755 " + MINITOUCH_PATH);
            int chmodExitCode = chmodProcess.waitFor();
            if (chmodExitCode != 0) {
                throw new RuntimeException("minitouch权限设置失败，退出码: " + chmodExitCode);
            }

            log.info("{}推送minitouch且设置权限成功", getDeviceName());
        } catch (Exception e) {
            log.error("{}推送minitouch时出错: {}", getDeviceName(), e.getMessage(), e);
            throw new RuntimeException("minitouch推送失败", e);
        }
    }

    /**
     * 确保minitouch已推送并具有正确的权限。
     */
    public void ensureMinitouchPushed() {
        if (!isMinitouchPushed()) {
            log.info("{}多指滑动中，minitouch的推送与权限检查存在问题，开始重新推送minitouch，并设置权限", getDeviceName());

            // 获取设备架构
            String architecture = getDeviceArchitecture();

            // 推送适配的minitouch
            pushMinitouch(architecture);

            // 再次检查minitouch是否推送成功
            if (!isMinitouchPushed()) {
                throw new RuntimeException("minitouch推送失败");
            }
        }
    }
}