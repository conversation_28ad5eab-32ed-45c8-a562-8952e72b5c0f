package com.desaysv.workserver.devices.android;

import lombok.Getter;

// 支持的视频编码格式
@Getter
public enum VideoCodec {
    H264("h264", "H.264", "最广泛支持，兼容性好"),
    H265("h265", "H.265/HEVC", "压缩率高，但编码较慢"),
    VP8("vp8", "VP8", "WebM 格式，Web 支持好"),
    VP9("vp9", "VP9", "Google 开源，压缩率高");

    private final String codecName;
    private final String displayName;
    private final String description;

    VideoCodec(String codecName, String displayName, String description) {
        this.codecName = codecName;
        this.displayName = displayName;
        this.description = description;
    }

}