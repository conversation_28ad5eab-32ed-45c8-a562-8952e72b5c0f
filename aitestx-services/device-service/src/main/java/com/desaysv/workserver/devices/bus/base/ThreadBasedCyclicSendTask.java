package com.desaysv.workserver.devices.bus.base;

import com.desaysv.workserver.devices.bus.base.can.CanBus;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessageEventListener;
import com.desaysv.workserver.devices.bus.base.can.E2eError;
import com.desaysv.workserver.devices.bus.base.frexray.FlexrayMessage;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.StrUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 基于线程的周期发送任务
 */
@Slf4j
public class ThreadBasedCyclicSendTask extends CyclicSendTask implements ModifiableCyclicTask, RestartableCyclicTask {

    private static final int MAX_RETRY_COUNT = 5_000_000; // 最大重试次数
    private final ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
    private final AtomicBoolean stopped = new AtomicBoolean(false);
    private final AtomicBoolean isPaused = new AtomicBoolean(false);
    private final Condition pauseCondition;
    private final CanBus bus;
    private Thread thread;
    private Long endTime;
    private CanMessageEventListener canMessageEventListener;
    private CycleSendRunnable cycleSendRunnable;

    public ThreadBasedCyclicSendTask(CanBus bus, CanMessage message, float period, Float duration, CanMessageEventListener canMessageEventListener) {
        super(message, period);
        this.bus = bus;
        this.canMessageEventListener = canMessageEventListener;
        pauseCondition = rwLock.writeLock().newCondition();
        setDuration(duration);
        start();
    }

    public ThreadBasedCyclicSendTask(CanBus bus, FlexrayMessage message, float period, Float duration) {
        super(message, period);
        this.bus = bus;
        pauseCondition = rwLock.writeLock().newCondition();
        setDuration(duration);
        start();
    }

    public void setDuration(Float duration) {
        if (!Objects.equals(duration, getDuration())) {
            log.info("更新报文持续时间:{}s", duration);
            super.setDuration(duration);
            if (duration != null) {
                endTime = System.currentTimeMillis() + new Float(duration * 1000).longValue();
            }
        }
    }

    public void setE2eType(String e2eType) {
        if (!Objects.equals(e2eType, getCanMessage().getE2eType())) {
            log.info("更新报文e2eType:{}", e2eType);
            getCanMessage().setE2eType(e2eType);
        }
    }

    public void enableE2e(boolean isE2eEnabled) {
        if (!Objects.equals(isE2eEnabled, getCanMessage().isE2eEnabled())) {
            log.info("更新报文openE2e:{}", isE2eEnabled);
            getCanMessage().setE2eEnabled(isE2eEnabled);
        }
    }

    @Override
    public void start() {
        if (thread == null || !thread.isAlive()) {
            stopped.set(false);
            isPaused.set(false);
            String name;
            if (getCanMessage() != null) {
                name = String.format("Cyclic Message for 0x%X [Can#%d-%d]", getCanMessage().getArbitrationId(), bus.getDeviceIndex(), getCanMessage().getChannel());
                cycleSendRunnable = new CycleSendRunnable();
                setCanMessageEventListener(canMessageEventListener);
                thread = new Thread(cycleSendRunnable, name);
            } else {
                //Flexray
                name = String.format("Cyclic Message for 0x%X [FrexRay#%d-%d]", getFlexrayMessage().getSlotId(), bus.getDeviceIndex(), getFlexrayMessage().getChannel());
                thread = new Thread(new CycleSendFlexrayRunnable(), name);
            }
            thread.setDaemon(true);
            thread.start();
        }
    }

    @Setter
    @Getter
    private class CycleSendRunnable implements Runnable {

        private CanMessageEventListener canMessageEventListener;

        @Override
        public void run() {
            //FIXME：还没实现连续同一个id的事件帧报文互斥逻辑
            int counter = 0;
            int loopIndex = 0;
            int retryCount = 0;
            boolean hasLoggedError = false;
            while (!stopped.get()) {
                if (isPaused.get()) {
                    waitForResume();
                    continue;
                }
                CanMessage message = getCanMessage();
                if (canMessageEventListener != null) {
                    canMessageEventListener.onMessageSend(message);
                }
                long started = System.currentTimeMillis();
                try {
                    //连续帧发送
                    for (int i = 0; i < Math.max(1, message.getFramesPerSendNum()); i++) {
                        if (message.isLoop() && message.getLoopDatas() != null && !message.getLoopDatas().isEmpty()) {
                            byte[] loopData = message.getLoopDatas().get(loopIndex);
                            message.setData(loopData);
                            loopIndex = (loopIndex + 1) % message.getLoopDatas().size();
                        }
                        bus.send(message);
                    }
                    hasLoggedError = false;
                    if (message.getSendTimes() > 0) {
                        counter++;
                    }
                } catch (Throwable e) {
                    retryCount++;
                    if (!hasLoggedError) {
                        log.error(String.format("CAN通道%d报文%s发送异常", getCanMessage().getChannel(), getCanMessage().getHexId()), e);
                        bus.sendError(getCanMessage());
                        hasLoggedError = true;
                    }
                    if (retryCount >= MAX_RETRY_COUNT) {
                        log.error("CAN发送失败超过最大重试次数{}，停止重试:{}", MAX_RETRY_COUNT, message);
                        stop(true);
                        return;
                    }
                }
                if (shouldStop(message, counter)) {
                    //发送完成
                    bus.sendFinish(getCanMessage());
                    stop(true);
                    break;
                }
                //补偿bus发送的耗时
                long delay = (long) (getPeriod() * 1000 - (System.currentTimeMillis() - started));
//                log.info("补偿bus发送的耗时:{}", delay);
                if (delay > 0) {
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException e) {
                        log.warn("周期发送任务被中断", e);
                        Thread.currentThread().interrupt();
                        return;
                    }
                } else {
                    Thread.yield();  // 让出CPU时间片
                }
            }
        }

    }

    private class CycleSendFlexrayRunnable implements Runnable {

        @Override
        public void run() {
            int counter = 0;
            while (!stopped.get()) {
                if (isPaused.get()) {
                    waitForResume();
                    continue;
                }
                FlexrayMessage message = getFlexrayMessage();
                long started = System.currentTimeMillis();
                try {
                    for (int i = 0; i < Math.max(1, message.getFramesPerSendNum()); i++) {
                        bus.send(message);
                    }
                    if (message.getSendTimes() > 0) {
                        counter++;
                    }
                } catch (Throwable e) {
                    log.error("Flexray发送异常:{}", e.getMessage(), e);
                    //TODO 发送异常通知前端
                }
                if (shouldStop(message, counter)) {
                    stop(true);
                    //TODO 发送完成通知前端
                    break;
                }
                //补偿bus发送的耗时
                long delay = (long) (getPeriod() * 1000 - (System.currentTimeMillis() - started));
                if (delay > 0) {
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException e) {
                        log.warn("周期发送任务被中断", e);
                        Thread.currentThread().interrupt();
                        return;
                    }
                } else {
                    Thread.yield();  // 让出CPU时间片
                }
            }
        }

    }

    private boolean shouldStop(CanMessage message, int counter) {
        if (message.getSendTimes() > 0 && counter == message.getSendTimes()) {
            //事件帧发送
            log.info("CAN报文{}的{}次事件帧发送完成", message.getHexId(), message.getSendTimes());
            return true;
        } else if (endTime != null && System.currentTimeMillis() >= endTime) {
            log.info("CAN报文{}整个周期发送完成", message.getHexId());
            return true;
        }
        return false;
    }

    private boolean shouldStop(FlexrayMessage message, int counter) {
        if (message.getSendTimes() > 0 && counter == message.getSendTimes()) {
            //事件帧发送
            log.info("FlexRay报文{}的{}次事件帧发送完成", message.getHexId(), message.getSendTimes());
            return true;
        } else if (endTime != null && System.currentTimeMillis() >= endTime) {
            log.info("FlexRay报文{}整个周期发送完成", message.getHexId());
            return true;
        }
        return false;
    }


    private void waitForResume() {
        rwLock.writeLock().lock();
        try {
            while (isPaused.get()) {
                try {
                    pauseCondition.await();
                } catch (InterruptedException e) {
                    log.warn("等待恢复时被中断", e);
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    @Override
    protected void _stopTask() {
//        if (stopped) {
//            throw new CanError("CAN报文已经停止");
//        }
        stopped.set(true);
        //thread.interrupt();
    }

    @Override
    public void modifyData(byte[] newData) {
        rwLock.writeLock().lock();
        try {
            byte[] oldData = getCanMessage().getData();
            if (!Arrays.equals(oldData, newData)) {
                System.arraycopy(newData, 0, oldData, 0, oldData.length);
                getCanMessage().setData(oldData);
                log.info("CAN报文{}数据更新为:{}", getCanMessage().getHexId(), ByteUtils.byteArrayToHexString(oldData));
            } else {
                log.info("CAN报文数据跟上次一致:{}", ByteUtils.byteArrayToHexString(oldData));
            }
        } finally {
            rwLock.writeLock().unlock();
        }
    }
    @Override
    public void modifyE2eError(E2eError e2eError){
        rwLock.writeLock().lock();
        try {
            getCanMessage().setE2eError(e2eError);
            log.info("CAN报文E2E错误配置更新");
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    @Override
    public void modifyLoopData(List<byte[]> newLoopDatas) {
        rwLock.writeLock().lock();
        try {
            List<byte[]> oldLoopDatas = getCanMessage().getLoopDatas();
            boolean dataChanged = !Arrays.deepEquals(oldLoopDatas.toArray(), newLoopDatas.toArray());
//            boolean dataChanged = false;
//            if (oldLoopDatas.size() != newLoopDatas.size()) {
//                dataChanged = true;
//            } else {
//                for (int i = 0; i < oldLoopDatas.size(); i++) {
//                    if (!Arrays.equals(oldLoopDatas.get(i), newLoopDatas.get(i))) {
//                        dataChanged = true;
//                        break;
//                    }
//                }
//            }
            if (dataChanged) {
                getCanMessage().setLoopDatas(newLoopDatas);
                log.info("更新CAN报文{}循环数据:{}", getCanMessage().getHexId(), StrUtils.getHexStringWithBlank(newLoopDatas));
            }
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    @Override
    public void modifyData(FlexrayMessage message) {
        rwLock.writeLock().lock();
        try {
            byte[] oldData = getFlexrayMessage().getData();
//        byte[] newData = message.getData();
            if (!Arrays.equals(oldData, message.getData())) {
                log.info("更新flexRay报文{}数据:{}", getFlexrayMessage().getHexId(), ByteUtils.byteArrayToHexString(oldData));
                System.arraycopy(message.getData(), 0, oldData, 0, oldData.length);
                getFlexrayMessage().setData(oldData);
            } else {
                log.info("frexRay报文数据跟上次一致");
            }
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    @Override
    public void modifyPeriod(float period) {
        rwLock.writeLock().lock();
        try {
            setPeriod(period);
        } finally {
            rwLock.writeLock().unlock();
        }
    }


    @Override
    public void modifyDlc(int dlc) {
        rwLock.writeLock().lock();
        try {
            log.info("更新CAN报文{}DLC:{}", getCanMessage().getHexId(), dlc);
            ByteUtils.adjustByteArray(getCanMessage().getData(), dlc);
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    @Override
    public void pause() {
        log.info("通道{}暂停报文:{}", getCanMessage().getChannel(), getCanMessage().getHexId());
        isPaused.set(true);
    }

    @Override
    public void resume() {
        log.info("通道{}正在恢复报文:{}", getCanMessage().getChannel(), getCanMessage().getHexId());
        rwLock.writeLock().lock();
        try {
            isPaused.set(false);
            pauseCondition.signalAll(); // 唤醒等待的线程
        } finally {
            rwLock.writeLock().unlock();
        }
        log.info("通道{}完成恢复报文:{}", getCanMessage().getChannel(), getCanMessage().getHexId());
    }

    public CanMessageEventListener getCanMessageEventListener() {
        return cycleSendRunnable.getCanMessageEventListener();
    }

    public void setCanMessageEventListener(CanMessageEventListener canMessageEventListener) {
        this.canMessageEventListener = canMessageEventListener;
        cycleSendRunnable.setCanMessageEventListener(canMessageEventListener);
    }

    public static void main(String[] args) {
        byte[] data = new byte[]{1, 2, 3, 4};
        int dlc = 2;
        byte[] bytes = ByteUtils.adjustByteArray(data, dlc);
        System.out.println(Arrays.toString(bytes));
    }
}
