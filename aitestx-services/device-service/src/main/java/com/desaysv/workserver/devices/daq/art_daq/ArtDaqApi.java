package com.desaysv.workserver.devices.daq.art_daq;

import com.sun.jna.Pointer;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.PointerByReference;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.function.Consumer;

/**
 * ArtDaqApi 类提供了与 ArtDAQ 数据采集设备（如 USB3200N）交互的 Java 接口封装。
 * 该类通过 JNA 调用底层 C 接口，实现设备控制、数据采集和信号处理功能。
 *
 * 主要功能包括：
 * 1. 设备管理：初始化、配置和释放数据采集设备
 * 2. 数据采集：支持单次和连续采集模拟输入信号
 * 3. 信号处理：计算 RMS 值和分贝值，支持环境噪声校准
 * 4. 实时处理：提供回调机制支持实时数据处理
 */
@Slf4j
public class ArtDaqApi {//usb3200n
    private static final ArtDaq.Clibrary cliInst = ArtDaq.Clibrary.INSTANCE;//JNI库实例，用于调用底层C库函数
    private Pointer taskHandle;//任务句柄指针，用于标识和管理数据采集任务

    @Getter
    private ArtDaqConfig config;//数据采集配置对象，包含采集参数和设置信息
    @Setter
    @Getter
    private double referenceValue=0.007;//参考值，用于计算分贝值
    private static final int MAX_RETRIES = 1;//最大重试次数

    /**
     * 默认构造函数，使用默认配置初始化 ArtDaqApi 实例
     */
    public ArtDaqApi() {
        this.config = new ArtDaqConfig();
    }

    /**
     * 构造函数，使用指定配置初始化 ArtDaqApi 实例
     * @param config 数据采集配置对象，如果为 null 则使用默认配置
     */
    public ArtDaqApi(ArtDaqConfig config) {
        this.config = config != null ? config : new ArtDaqConfig();
    }

    /**
     * 设置数据采集配置
     * @param config 数据采集配置对象，如果为 null 则使用默认配置
     */
    public void setConfig(ArtDaqConfig config) {
        this.config = config != null ? config : new ArtDaqConfig();
    }

    /**
     * ArtDaqError 是 ArtDaqApi 中使用的自定义异常类，
     * 用于封装底层库返回的错误码和错误信息
     */
    public static class ArtDaqError extends Exception {

        /**
         * 构造函数，根据错误码创建异常对象
         * @param error 错误码
         */
        public ArtDaqError(int error) {
            super(getErrorString(error));
        }

        /**
         * 构造函数，根据错误码和自定义消息创建异常对象
         * @param error 错误码
         * @param message 自定义错误消息
         */
        public ArtDaqError(int error, String message) {
            super(message + " (Error " + error + "): " + getErrorString(error));
        }
    }

    /**
     * 获取系统中所有可用的设备名称
     * @return 设备名称字符串，多个设备名称用逗号分隔
     */
    public String getDeviceName() {
        byte[] attribute = new byte[2048];
        int error = cliInst.ArtDAQ_GetSystemAttribute(ArtDaq.ArtDAQ_Sys_DevNames, attribute, attribute.length);
        if (error < 0) {
            String errorMsg = getErrorString(error);
            log.error("获取设备名称失败，错误码：{}，错误信息：{}", error, errorMsg);
            return "";
        }
        // 替换连续空字符为逗号，并去除末尾空值
        String deviceNamesStr = new String(attribute, StandardCharsets.UTF_8).trim();
        deviceNamesStr = deviceNamesStr.replaceAll("\\u0000+", ","); // 替换多个空字符为逗号
        deviceNamesStr = deviceNamesStr.replaceAll(",$", ""); // 去除末尾逗号
        return deviceNamesStr;
    }

    /**
     * 校准参考值 - 通过采集当前环境噪声来设置合适的参考值
     * @return 校准后的参考值
     */
    public double calibrateReferenceValue() throws ArtDaqError {
        log.info("开始校准参考值...");

        // 采集3秒的环境噪声数据
        List<Double> calibrationData = collectAudioData(3.0);

        if (calibrationData.isEmpty()) {
            throw new ArtDaqError(-1, "校准数据采集失败");
        }

        // 计算RMS值
        double rms = calculateRMS(calibrationData);

        // 设置参考值为当前RMS值的80%，确保分贝值为正
        double newReferenceValue = rms * 0.8;

        // 确保参考值不会太小
        if (newReferenceValue < 0.001) {
            newReferenceValue = 0.001;
        }

        this.referenceValue = newReferenceValue;

        log.info("校准完成，新的参考值: {}", this.referenceValue);
        return this.referenceValue;
    }

    /**
     * 初始化数据采集设备
     * @param deviceName 设备名称，格式应为 "DevX" 形式
     * @return 初始化成功返回 true，否则返回 false
     */
    public boolean init(String deviceName) {
        // 增加设备名称校验
        if (!deviceName.matches("^Dev\\d+$")) {
            log.error("无效的设备名称格式: {}", deviceName);
            return false;
        }
        // 增加设备存在性检查
        if (!getAllDevices().contains(deviceName)) {
            log.error("设备 {} 不存在", deviceName);
            return false;
        }

        try {
            log.info("开始初始化设备: {}", deviceName);

            // 检查设备是否被占用
            if (isDeviceOccupied(deviceName)) {
                log.warn("设备 {} 可能被占用，尝试强制释放资源...", deviceName);
                forceReleaseAllTasks();

                // 再次检查
                if (isDeviceOccupied(deviceName)) {
                    log.error("设备 {} 仍然被占用，无法初始化", deviceName);
                    return false;
                }
            }

            // 确保当前实例的资源已释放
            if (taskHandle != null) {
                log.info("释放当前实例的旧任务句柄");
                release();
                taskHandle = null;
            }

            // 正常初始化流程
            taskHandle = createTask();
            log.debug("任务句柄创建成功: {}", taskHandle);

            byte[] chanName = getChanName(deviceName);
            createChannel(taskHandle, chanName);
            log.info("模拟输入通道创建成功");

            configClock(taskHandle);
            log.info("采样时钟配置成功");

            configTrigger(taskHandle, chanName);
            log.info("触发配置成功");

            startTask(taskHandle);
            log.info("任务启动成功");

            // 校准参考值
            calibrateReferenceValue();
            log.info("参考值校准完成");

            return true;

        } catch (ArtDaqError e) {
            log.error("初始化过程中出现错误", e);
            if (taskHandle != null) {
                cliInst.ArtDAQ_ClearTask(taskHandle);
                taskHandle = null;
            }
            return false;
        }
    }

    /**
     * 获取系统中所有可用的设备列表
     * @return 包含所有设备名称的列表
     */
    private List<String> getAllDevices() {
        byte[] devices = new byte[2048];
        int error = cliInst.ArtDAQ_GetSystemAttribute(ArtDaq.ArtDAQ_Sys_DevNames, devices, devices.length);
        return Arrays.stream(new String(devices).split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 释放数据采集资源，停止并清除任务
     */
    public void release() {
        if (taskHandle != null) {
            try {
                log.info("正在释放DAQ任务资源...");

                // 停止任务
                int stopError = cliInst.ArtDAQ_StopTask(taskHandle);
                if (stopError < 0) {
                    log.warn("停止任务时出现警告: {}", getErrorString(stopError));
                }

                // 清除任务
                int clearError = cliInst.ArtDAQ_ClearTask(taskHandle);
                if (clearError < 0) {
                    log.warn("清除任务时出现警告: {}", getErrorString(clearError));
                }

                taskHandle = null;
                log.info("DAQ任务资源释放完成");

            } catch (Exception e) {
                log.error("释放资源时出现异常", e);
            }
        }
    }

    /**
     * 强制释放所有DAQ任务资源
     */
    public void forceReleaseAllTasks() {
        try {
            log.info("开始强制释放所有DAQ任务资源...");

            // 尝试清除所有可能存在的任务
            for (int i = 0; i < 10; i++) {
                try {
                    // 创建临时任务句柄来清除可能存在的任务
                    PointerByReference tempTaskPointer = new PointerByReference();
                    String tempTaskName = "TempTask_" + i;
                    int createError = cliInst.ArtDAQ_CreateTask(tempTaskName.getBytes(), tempTaskPointer);

                    if (createError >= 0) {
                        Pointer tempHandle = tempTaskPointer.getValue();
                        cliInst.ArtDAQ_StopTask(tempHandle);
                        cliInst.ArtDAQ_ClearTask(tempHandle);
                    }
                } catch (Exception e) {
                    // 忽略清理过程中的异常
                }
            }

            // 重置设备
            int resetError = cliInst.ArtDAQ_ResetDevice("".getBytes());
            if (resetError < 0) {
                log.warn("设备重置警告: {}", getErrorString(resetError));
            } else {
                log.info("设备重置成功");
            }

            // 等待设备稳定
            Thread.sleep(2000);

        } catch (Exception e) {
            log.error("强制释放资源时出现异常", e);
        }
    }

    /**
     * 检查设备是否被占用
     */
    private boolean isDeviceOccupied(String deviceName) {
        try {
            // 尝试创建一个临时任务来测试设备是否可用
            String testTaskName = "TestTask_" + System.currentTimeMillis();
            PointerByReference testTaskPointer = new PointerByReference();

            int createError = cliInst.ArtDAQ_CreateTask(testTaskName.getBytes(), testTaskPointer);
            if (createError < 0) {
                return true; // 创建失败，可能被占用
            }

            Pointer testHandle = testTaskPointer.getValue();
            byte[] chanName = getChanName(deviceName);

            // 尝试创建通道
            int channelError = cliInst.ArtDAQ_CreateAIVoltageChan(testHandle, chanName, "".getBytes(),
                    config.getTerminalConfig(), config.getMinVal(), config.getMaxVal(),
                    ArtDaq.ArtDAQ_Val_Volts, null);

            // 清理测试任务
            cliInst.ArtDAQ_ClearTask(testHandle);

            return channelError < 0; // 如果通道创建失败，设备可能被占用

        } catch (Exception e) {
            log.warn("检查设备占用状态时出现异常", e);
            return true; // 出现异常，假设被占用
        }
    }

    /**
     * 根据错误码获取对应的错误描述信息
     * @param errorCode 错误码
     * @return 错误描述字符串，如果错误码非负则返回 null
     */
    private static String getErrorString(int errorCode) {
        if (errorCode < 0) {
            byte[] errorString = new byte[2048];
            cliInst.ArtDAQ_GetExtendedErrorInfo(errorString, 2048);
            return new String(errorString);
        }
        return null;
    }

    /**
     * 根据设备名称生成通道名称
     * @param deviceName 设备名称
     * @return 通道名称字节数组
     */
    private byte[] getChanName(String deviceName) {
        String strChannelName = deviceName.split(",")[0].trim() + "/" + "ai0\0";
        return strChannelName.getBytes();
    }

    /**
     * 创建数据采集任务
     * @return 任务句柄指针，创建失败返回 null
     */
    private Pointer createTask() {
        // 修改任务名为符合NI规范的名称
        String strTaskName = "MyDAQTask_01"; // 移除特殊字符和终止符
        byte[] TaskName = strTaskName.getBytes(StandardCharsets.US_ASCII);

        PointerByReference taskPointer = new PointerByReference();
        int error = cliInst.ArtDAQ_CreateTask(TaskName, taskPointer);

        if (error < 0) {
            String errorMsg = getErrorString(error);
            log.error("任务创建失败 (Error {}): {}", error, errorMsg);
            return null;
        }
        return taskPointer.getValue();
    }

    /**
     * 创建模拟输入电压通道
     * @param taskHandle 任务句柄
     * @param chanName 通道名称
     * @throws ArtDaqError 创建失败时抛出异常
     */
    private void createChannel(Pointer taskHandle, byte[] chanName) throws ArtDaqError {
        String strNameToAssignToChannel = "";
        byte[] nameToAssignToChannel = strNameToAssignToChannel.getBytes();
        int error = cliInst.ArtDAQ_CreateAIVoltageChan(taskHandle, chanName, nameToAssignToChannel,
                config.getTerminalConfig(), config.getMinVal(), config.getMaxVal(),
                ArtDaq.ArtDAQ_Val_Volts, null);

        if (error < 0) {
            log.error("通道创建失败 (Min: {}V, Max: {}V, Terminal: {})",
                    config.getMinVal(), config.getMaxVal(), config.getTerminalConfig());
            throw new ArtDaqError(error);
        }
    }

    /**
     * 配置采样时钟
     * @param taskHandle 任务句柄
     * @throws ArtDaqError 配置失败时抛出异常
     */
    private void configClock(Pointer taskHandle) throws ArtDaqError {
        int error = cliInst.ArtDAQ_CfgSampClkTiming(
                taskHandle,
                "".getBytes(),
                config.getSampleRate(),
                ArtDaq.ArtDAQ_Val_Rising,
                ArtDaq.ArtDAQ_Val_ContSamps,
                config.getBufferSize()
        );

        if (error < 0) {
            log.error("时钟配置失败 (Rate: {}Hz, Buffer: {})", config.getSampleRate(), config.getBufferSize());
            throw new ArtDaqError(error);
        }
    }

    /**
     * 配置触发参数
     * @param taskHandle 任务句柄
     * @param chanName 通道名称
     * @throws ArtDaqError 配置失败时抛出异常
     */
    private void configTrigger(Pointer taskHandle, byte[] chanName) throws ArtDaqError {
        // 可以考虑不使用触发，直接开始采集
        // 注释掉触发配置，让任务立即开始采集
        /*
        int error = cliInst.ArtDAQ_CfgAnlgEdgeStartTrig(
                taskHandle,
                chanName,
                ArtDaq.ArtDAQ_Val_Rising,
                config.getTriggerLevel()
        );

        if (error < 0) {
            log.error("触发配置失败 (Level: {}V)", config.getTriggerLevel());
            throw new ArtDaqError(error);
        }
        */
        log.info("跳过触发配置，使用立即开始模式");
    }

    /**
     * 启动数据采集任务
     * @param taskHandle 任务句柄
     * @throws ArtDaqError 启动失败时抛出异常
     */
    private void startTask(Pointer taskHandle) throws ArtDaqError {
        int error = cliInst.ArtDAQ_StartTask(taskHandle);
        if (error < 0) {
            throw new ArtDaqError(error);
        }
        try {
            // 增加等待时间，让缓冲区有足够数据
            Thread.sleep(1000); // 等待1秒确保任务开始并有数据
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 读取单次模拟输入数据并计算RMS值
     * @return 采集数据的RMS值
     * @throws ArtDaqError 读取失败时抛出异常
     */
    public double read() throws ArtDaqError {
        int arraySizeInSamples = 1000;
        double[] data = new double[arraySizeInSamples];
        IntByReference samplesPerChanRead = new IntByReference(0);
        IntByReference reserved = new IntByReference(0);

        int error = cliInst.ArtDAQ_ReadAnalogF64(taskHandle, 1000, 100.0, ArtDaq.ArtDAQ_Val_GroupByScanNumber,
                data, arraySizeInSamples, samplesPerChanRead, reserved);
        if (error < 0) {
            throw new ArtDaqError(error);
        }
        int samples = samplesPerChanRead.getValue();
        if (samples <= 0) {
            throw new ArtDaqError(-200284);
        }
        System.out.printf("Acquired %d samples\n", samples);
        for (int k = 0; k < samples; k++) {
            System.out.printf("  %.04f  ", data[k]);
        }
        System.out.println("\n");

        // 计算有效值（RMS）
        double sum = 0.0;
        for (int i = 0; i < samples; i++) {
            sum += data[i] * data[i]; // 累加平方值
        }
        // 计算均方根
        return Math.sqrt(sum / samples);
    }

    /**
     * 连续采集音频数据，支持实时处理
     * @param durationSeconds 采集时间（秒）
     * @param dataProcessor 数据处理回调
     */
    public void readContinuousWithCallback(double durationSeconds,
                                           Consumer<double[]> dataProcessor) throws ArtDaqError {
        // 重新启动任务并清空缓冲区
        restartTask();
        flushBuffer();
        long startTime = System.currentTimeMillis();
        long durationMs = (long) (durationSeconds * 1000);
        log.info("开始连续采集 {} 秒的音频数据（回调模式）", durationSeconds);
        while (System.currentTimeMillis() - startTime < durationMs) {
            try {
                double[] data = readRawDataWithRetry(); // 使用带重试的读取方法
                if (data.length > 0 && dataProcessor != null) {
                    dataProcessor.accept(data);
                }
                Thread.sleep(50); // 增加间隔时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 采集音频数据并保存到列表
     * @param durationSeconds 采集时间（秒）
     * @return 所有采集的原始数据
     */
    public List<Double> collectAudioData(double durationSeconds) throws ArtDaqError {
        List<Double> audioData = new ArrayList<>();

        readContinuousWithCallback(durationSeconds, data -> {
            for (double value : data) {
                audioData.add(value);
            }
        });

        return audioData;
    }

    /**
     * 读取原始数据（不计算RMS）
     * @return 采集到的原始数据数组
     * @throws ArtDaqError 读取失败时抛出异常
     */
    private double[] readRawData() throws ArtDaqError {
        int arraySizeInSamples = 1000;
        double[] data = new double[arraySizeInSamples];
        IntByReference samplesPerChanRead = new IntByReference(0);
        IntByReference reserved = new IntByReference(0);

        // 增加超时时间从1.0秒到10.0秒
        int error = cliInst.ArtDAQ_ReadAnalogF64(taskHandle, 1000, 10.0,
                ArtDaq.ArtDAQ_Val_GroupByScanNumber, data, arraySizeInSamples,
                samplesPerChanRead, reserved);

        if (error < 0) {
            throw new ArtDaqError(error);
        }

        int samples = samplesPerChanRead.getValue();
        if (samples <= 0) {
            return new double[0];
        }

        return Arrays.copyOf(data, samples);
    }

    /**
     * 带重试机制的数据读取
     * @return 采集到的原始数据数组
     * @throws ArtDaqError 读取失败时抛出异常
     */
    private double[] readRawDataWithRetry() throws ArtDaqError {
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                return readLatestData();
            } catch (ArtDaqError e) {
                if (e.getMessage().contains("-200284")) { // SamplesNotYetAvailable
                    retryCount++;
                    log.warn("数据未准备好，重试 {}/{}", retryCount, maxRetries);
                    try {
                        Thread.sleep(100); // 等待100ms后重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw e;
                    }
                } else {
                    throw e; // 其他错误直接抛出
                }
            }
        }
        throw new ArtDaqError(-200284); // 重试失败
    }

    /**
     * 连续采集指定时间的音频数据
     * @param durationSeconds 采集时间（秒）
     * @return 所有采集数据的RMS值
     */
    public double readContinuous(double durationSeconds) throws ArtDaqError {
        List<Double> allData = new ArrayList<>();
        // 重新启动任务并清空缓冲区
        restartTask();
        flushBuffer();
        long startTime = System.currentTimeMillis();
        long durationMs = (long) (durationSeconds * 1000);
        log.info("开始连续采集 {} 秒的音频数据", durationSeconds);
        while (System.currentTimeMillis() - startTime < durationMs) {
            try {
                double[] singleRead = readRawData();
                for (double value : singleRead) {
                    allData.add(value);
                }

                // 短暂休眠避免过度占用CPU
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        log.info("采集完成，共获得 {} 个样本", allData.size());
        return calculateRMS(allData);
    }

    /**
     * 计算RMS值
     * @param data 输入数据列表
     * @return 计算得到的RMS值
     */
    public double calculateRMS(List<Double> data) {
        if (data.isEmpty()) {
            return 0.0;
        }

        double sum = 0.0;
        for (double value : data) {
            sum += value * value;
        }
        return Math.sqrt(sum / data.size());
    }

    /**
     * 计算分贝值
     * @param rmsValue RMS值
     * @return 对应的分贝值
     */
    public double calculateDecibel(double rmsValue) {
        return 20 * Math.log10(rmsValue/ referenceValue);
    }

    /**
     * 清空缓冲区中的旧数据
     * @throws ArtDaqError 清空过程中出现错误时抛出异常
     */
    private void flushBuffer() throws ArtDaqError {
        log.debug("开始清空缓冲区旧数据...");
        try {
            // 多次读取并丢弃数据，确保清空缓冲区
            for (int i = 0; i < 10; i++) {
                try {
                    readRawData();
                    Thread.sleep(50); // 等待新数据填充
                } catch (ArtDaqError e) {
                    // 如果没有数据可读，说明缓冲区已清空
                    if (e.getMessage().contains("-200284")) {
                        break;
                    }
                    throw e;
                }
            }
            log.debug("缓冲区清空完成");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ArtDaqError(-1, "清空缓冲区时被中断");
        }
    }

    /**
     * 重新启动任务以确保数据新鲜
     * @throws ArtDaqError 重启过程中出现错误时抛出异常
     */
    private void restartTask() throws ArtDaqError {
        log.debug("重新启动采集任务...");

        // 停止当前任务
        int stopError = cliInst.ArtDAQ_StopTask(taskHandle);
        if (stopError < 0) {
            log.warn("停止任务时出现警告: {}", getErrorString(stopError));
        }

        // 重新启动任务
        int startError = cliInst.ArtDAQ_StartTask(taskHandle);
        if (startError < 0) {
            throw new ArtDaqError(startError, "重新启动任务失败");
        }

        // 等待任务稳定
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.debug("任务重新启动完成");
    }

    /**
     * 读取最新数据（跳过旧缓冲区数据）
     * @return 最新的原始数据数组
     * @throws ArtDaqError 读取过程中出现错误时抛出异常
     */
    private double[] readLatestData() throws ArtDaqError {
        // 先读取并丢弃旧数据
        try {
            for (int i = 0; i < MAX_RETRIES; i++) {
                readRawData(); // 丢弃前几次读取的数据
                Thread.sleep(20);
            }
        } catch (Exception e) {
            log.debug("清空旧数据时出现异常: {}", e.getMessage());
        }

        // 读取最新数据
        return readRawData();
    }

    // 使用示例
    public static void main(String[] args) {
        ArtDaqConfig config = new ArtDaqConfig(-10.0, 10.0, ArtDaq.ArtDAQ_Val_Diff, 5000.0);
        ArtDaqApi api = new ArtDaqApi(config);

        if (api.init("Dev1")) {
            try {
                // 方式1：连续采集5秒并计算总体RMS
                double rms = api.readContinuous(5.0);
                log.info("5秒音频数据RMS值: {}", rms);

                // 方式2：采集数据到列表进行后续处理
                List<Double> audioData = api.collectAudioData(3.0);
                log.info("3秒音频数据: {}", audioData);
                log.info("采集到 {} 个音频样本", audioData.size());

                // 方式3：实时处理数据
                api.readContinuousWithCallback(2.0, data -> {
                    double segmentRMS = api.calculateRMS(Arrays.asList(
                            Arrays.stream(data).boxed().toArray(Double[]::new)));
                    System.out.printf("实时RMS: %.4f\n", segmentRMS);
                });

            } catch (ArtDaqError e) {
                log.error("采集失败", e);
            } finally {
                api.release();
            }
        }
    }
}
