package com.desaysv.workserver.devices.serial.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.common.port.MessageText;
import com.desaysv.workserver.devices.serial.SerialJudgeContext;
import com.desaysv.workserver.entity.DeviceContextInfo;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.utils.ByteUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 串口操作接口
 */
public interface ISerial {
    Logger log = LogManager.getLogger(ISerial.class.getSimpleName());


    // 判断字符串是否是十六进制格式
    default boolean isHexadecimal(String message) {
        // 移除所有空格
        String cleanedMessage = message.replaceAll("\\s+", "");
        // 十六进制的正则表达式：必须是偶数长度的字符串，只包含0-9, a-f, A-F的字符
        String hexPattern = "^[0-9A-Fa-f]+$";
        // 检查是否符合正则表达式，并且长度是偶数
        return cleanedMessage.matches(hexPattern) && (cleanedMessage.length() % 2 == 0);
    }

    boolean send(String message, boolean isHex) throws DeviceSendException;

    boolean sendByActionSequence(String message, boolean isHex, String checkedContext) throws DeviceSendException;

    /**
     * 串口发送数据
     *
     * @param text 数据带有括号的值
     * @return
     * @throws DeviceSendException 发送失败
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SEND_PARENTHESIS"})
    default void send(String text, String parenthesisContext, String checkedContext) throws DeviceSendException {
        sendAnything(parenthesisContext, checkedContext, String.valueOf(50));
    }

    /**
     * 串口发送数据
     *
     * @param text 数据
     * @return
     * @throws DeviceSendException 发送失败
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SEND"})
    default void send(String text, String checkedContext) throws DeviceSendException {
        sendAnything(text, checkedContext, String.valueOf(50));
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SEND_BY_INTERPRETATION"})
    default boolean sendByInterpretation(String name, String checkedContext) throws DeviceSendException {
        return false;
    }

    default void sendAnything(String text, String checkedContext, String interval) throws DeviceSendException {
        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(interval);
        List<String> contexts = expandContextIfNecessary(checkedContext);

        if (text != null && text.length() >= 3 && text.charAt(text.length() - 3) == '~') {
            String[] parts = text.split("~");
            if (parts.length != 2) {
                throw new IllegalArgumentException("格式错误: 必须为 XXXXXXXX00~FF 格式");
            }

            String prefixWithStart = parts[0];
            String endStr = parts[1];

            if (prefixWithStart.length() < 2) {
                throw new IllegalArgumentException("前缀部分长度不足");
            }

            String prefix = prefixWithStart.substring(0, prefixWithStart.length() - 2);
            int start = Integer.parseInt(prefixWithStart.substring(prefixWithStart.length() - 2), 16);
            int end = Integer.parseInt(endStr, 16);

            if (start > end) {
                throw new IllegalArgumentException("起始值不能大于结束值");
            }

            boolean useSameContext = contexts.size() == 1;

            for (int i = start; i <= end; i++) {
                String hexValue = String.format("%02X", i);
                String message = prefix + hexValue;
                String dynamicContext = useSameContext ? contexts.get(0) : (i - start < contexts.size() ? contexts.get(i - start) : null);
                sendByActionSequence(message, isHexadecimal(message), dynamicContext);
                try {
                    TimeUnit.SECONDS.sleep(seconds.longValue());
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        } else if (text.contains("~")) {
            throw new IllegalArgumentException("格式错误: ~ 必须位于倒数第三位");
        } else {
            // 单条消息发送
            if (contexts.size() != 1) {
                throw new IllegalArgumentException("单条消息发送时，上下文不能为范围");
            }
            sendByActionSequence(text, isHexadecimal(text), appendChecksumToString(contexts.get(0)));
        }
    }

    default List<String> expandContextIfNecessary(String context) {
        if (context != null && context.length() >= 3 && context.charAt(context.length() - 3) == '~') {
            String[] parts = context.split("~");
            if (parts.length != 2) {
                throw new IllegalArgumentException("上下文格式错误: 必须为 XXXXXXXX00~FF 格式");
            }

            String prefixWithStart = parts[0];
            String endStr = parts[1];

            if (prefixWithStart.length() < 2) {
                throw new IllegalArgumentException("上下文格式错误: 前缀部分长度不足");
            }

            String prefix = prefixWithStart.substring(0, prefixWithStart.length() - 2);
            int start = Integer.parseInt(prefixWithStart.substring(prefixWithStart.length() - 2), 16);
            int end = Integer.parseInt(endStr, 16);

            if (start > end) {
                throw new IllegalArgumentException("上下文格式错误: 起始值不能大于结束值");
            }

            List<String> contexts = new ArrayList<>();
            for (int i = start; i <= end; i++) {
                String hexValue = String.format("%02X", i);
                contexts.add(prefix + hexValue);
            }
            return contexts;
        } else if (context != null && context.contains("~")) {
            throw new IllegalArgumentException("上下文格式错误: ~ 必须位于倒数第三位");
        } else {
            return Collections.singletonList(context);
        }
    }

    default void clearSerialRecvQueueManager() {
    }

    /**
     * 获取串口反馈
     *
     * @param isHex               是否为16进制
     * @param compareMessage      比较的消息
     * @param timeoutMilliseconds 接收超时时间
     * @return 反馈内容
     */
    String readPortFeedback(boolean isHex, String compareMessage, int timeoutMilliseconds);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).RECV_PARENTHESIS"})
    default ActualExpectedResult compareSerialFeedback(String text, String parenthesisContext) throws DeviceSendException {
        return compareFeedback(parenthesisContext);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).RECV"})
    default ActualExpectedResult compareFeedback(String message) {
        return compareFeedbackWithTimeout(message, "2000");
    }

    /**
     * 串口反馈比对
     *
     * @return 是否匹配
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).RECV_WAIT"})
    default ActualExpectedResult compareFeedbackWithTimeout(String message, String timeoutMilliseconds) {
        Float timeout = BaseRegexRule.getSecondsOfDefaultMills(timeoutMilliseconds);
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        log.info("读取串口反馈");

        boolean isHex = isHexadecimal(message);
        String recvMessage;

        if (message.length() >= 3 && message.charAt(message.length() - 3) == '~') {
            String[] parts = message.split("~");
            if (parts.length != 2) {
                throw new IllegalArgumentException("接收消息格式错误: 必须为 XXXXXXXX00~FF 格式");
            }

            String prefixWithStart = parts[0];
            String endStr = parts[1];

            if (prefixWithStart.length() < 2) {
                throw new IllegalArgumentException("接收消息格式错误: 前缀部分长度不足");
            }

            String prefix = prefixWithStart.substring(0, prefixWithStart.length() - 2);
            int start = Integer.parseInt(prefixWithStart.substring(prefixWithStart.length() - 2), 16);
            int end = Integer.parseInt(endStr, 16);

            if (start > end) {
                throw new IllegalArgumentException("接收消息格式错误: 起始值不能大于结束值");
            }

            Set<String> expectedMessages = new HashSet<>();
            for (int i = start; i <= end; i++) {
                String hexValue = String.format("%02X", i);
                expectedMessages.add(prefix + hexValue);
            }

            recvMessage = readPortFeedback(isHex, message, Math.round(timeout * 1000));
            log.info("接收串口消息:{}", recvMessage);

            String[] receivedLines = recvMessage.split("\n");
            boolean allMatched = true;

            for (String line : receivedLines) {
                String trimmedLine = line.trim();
                if (!expectedMessages.contains(trimmedLine)) {
                    allMatched = false;
                    break;
                }
            }
            clearSerialRecvQueueManager();
            actualExpectedResult.put("compareFeedback", allMatched, recvMessage);

        } else if (message.contains("~")) {
            throw new IllegalArgumentException("接收消息格式错误: ~ 必须位于倒数第三位");
        } else {
            recvMessage = readPortFeedback(isHex, message, Math.round(timeout * 1000));
            log.info("接收串口消息:{}", recvMessage);
            boolean pass = recvMessage.toLowerCase().contains(message.toLowerCase());
            actualExpectedResult.put("compareFeedback", pass, recvMessage);
        }

        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SEND_CHECK_SUM"})
    default void sendWithCheckSum(String text, String checkType, String checkedContext) throws DeviceSendException {
        sendAnything(text, checkType, String.valueOf(50), checkedContext);
    }

    /**
     * 串口发送数据
     *
     * @param text 数据
     * @return
     * @throws DeviceSendException 发送失败
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SEND_CHECK_INTERNAL"})
    default void sendAnything(String text, String checkType, String interval, String checkedContext) throws DeviceSendException {
        SerialJudgeContext.setContext(checkedContext);
        List<String> contexts = expandContextIfNecessary(text);
        List<String> checkedContexts = expandCheckedContextWithChecksum(checkedContext);

        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(interval);
        if (SerialJudgeContext.isReceiveContext()) {
            if (contexts.size() != checkedContexts.size()) {
                log.error("上下文数量不一致: {} != {}", contexts.size(), checkedContexts.size());
                return;
            }
        }
        if (text != null && hasValidRangeFormat(text)) {
            RangeInfo range = parseRange(text);
            for (int i = range.start; i <= range.end; i++) {
                String hexValue = String.format("%02X", i);
                String message = range.prefix + hexValue;
                String finalMessage = applyChecksumIfNecessary(message, checkType);
                boolean isHex = isHexadecimal(finalMessage);
                int index = i - range.start;
                if (SerialJudgeContext.isReceiveContext()) {
                    String dynamicCheckedContext = checkedContexts.get(index);
                    sendByActionSequence(finalMessage, isHex, dynamicCheckedContext);
                } else {
                    send(finalMessage, isHex);
                }
                try {
                    TimeUnit.SECONDS.sleep(seconds.longValue());
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            }
        } else if (text.contains("~")) {
            throw new IllegalArgumentException("格式错误: ~ 必须位于倒数第三位");
        } else {
            String finalMessage = applyChecksumIfNecessary(text, checkType);
            boolean isHex = isHexadecimal(finalMessage);
            if (contexts.size() != 1) {
                throw new IllegalArgumentException("单条消息发送时，上下文不能为范围");
            }
            sendByActionSequence(finalMessage, isHex, appendChecksumToString(checkedContext));
        }
    }


    default List<String> expandCheckedContextWithChecksum(String checkedContext) {
        List<String> result = new ArrayList<>();

        if (checkedContext != null && checkedContext.contains("~") && checkedContext.contains("-")) {
            int tildaIndex = checkedContext.lastIndexOf('~');
            int dashIndex = checkedContext.lastIndexOf('-');

            if (tildaIndex > 0 && tildaIndex < dashIndex) {
                String prefixWithStart = checkedContext.substring(0, tildaIndex);
                String endStr = checkedContext.substring(tildaIndex + 1, dashIndex);
                String checkType = checkedContext.substring(dashIndex + 1);

                String prefix = prefixWithStart.substring(0, prefixWithStart.length() - 2);
                String startStr = prefixWithStart.substring(prefixWithStart.length() - 2);
                int start = Integer.parseInt(startStr, 16);
                int end = Integer.parseInt(endStr, 16);

                if (start > end) {
                    throw new IllegalArgumentException("起始值不能大于结束值");
                }

                for (int i = start; i <= end; i++) {
                    String hexValue = String.format("%02X", i);
                    String message = prefix + hexValue;
                    String hexPart = extractHexPart(message);
                    String checkSummedMessage = applyChecksumIfNecessary(hexPart, checkType);
                    String checksum = checkSummedMessage.substring(hexPart.length()).trim();
                    result.add(message + " " + checksum);
                }
            } else {
                throw new IllegalArgumentException("格式错误: 必须为 XXXXXXXX00~FF-算法 格式");
            }
        } else {
            result.add(checkedContext);
        }

        return result;
    }

    default String appendChecksumToString(String checkedContext) {
        if (checkedContext == null) {
            return checkedContext;
        }

        if (checkedContext.contains("~")) {
            throw new IllegalArgumentException("该方法仅处理不包含 ~ 的字符串");
        }

        // 没有 '-' 直接返回原值
        if (!checkedContext.contains("-")) {
            return checkedContext;
        }

        String[] parts = checkedContext.split("-");

        String lastPart = parts[parts.length - 1].trim().toUpperCase();
        if (!isValidCheckType(lastPart)) {
            return checkedContext;
        }

        String checkType = lastPart;
        String messagePart = parts[parts.length - 2];

        String hexPart = extractHexPart(messagePart);
        String checkSummedMessage = applyChecksumIfNecessary(hexPart, checkType);
        String checksum = checkSummedMessage.substring(hexPart.length()).trim();

        return checkedContext.replace(messagePart, messagePart + " " + checksum);
    }

    /**
     * 判断是否是支持的校验类型
     */
    default boolean isValidCheckType(String type) {
        return "ADD8".equals(type) || "XOR8".equals(type) || "CRC8".equals(type);
    }


    default String extractHexPart(String message) {
        String[] parts = message.split("-");
        if (parts.length == 0) {
            throw new IllegalArgumentException("无效格式，未找到十六进制数据部分: " + message);
        }

        String hexPart = parts[parts.length - 1].trim();
        if (!isLikelyHexFormat(hexPart)) {
            throw new IllegalArgumentException("最后一段不是有效的十六进制格式: " + hexPart);
        }

        return hexPart;
    }

    default boolean isLikelyHexFormat(String str) {
        return str.matches("^[0-9A-Fa-f]{2}(\\s+[0-9A-Fa-f]{2})*$");
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).RECV_CHECK"})
    default ActualExpectedResult compareFeedback(String message, String checkType) {
        return compareFeedback(message, checkType, "2000");
    }

    /**
     * 串口反馈比对
     *
     * @return 是否匹配
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).RECV_CHECK_WAIT"})
    default ActualExpectedResult compareFeedback(String message, String checkType, String TimeoutMilliseconds) {
        Float timeout = BaseRegexRule.getSecondsOfDefaultMills(TimeoutMilliseconds);
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        log.info("读取串口反馈");
        boolean isHex = isHexadecimal(message);

        if (hasValidRangeFormat(message)) {
            RangeInfo range = parseRange(message);
            Set<String> expectedMessages = new HashSet<>();
            for (int i = range.start; i <= range.end; i++) {
                String hexValue = String.format("%02X", i);
                String expectedMessage = applyChecksumIfNecessary(range.prefix + hexValue, checkType);
                expectedMessages.add(expectedMessage);
            }
            List<String> missingMessages = new ArrayList<>();
            for (String expectedMessage : expectedMessages) {
                String recvMessage = readPortFeedback(isHex, expectedMessage, Math.round(timeout * 1000));
                if (recvMessage == null || !recvMessage.toLowerCase().contains(expectedMessage)) {
                    missingMessages.add(recvMessage);
                }
            }
            boolean allReceived = missingMessages.isEmpty();
            if (!allReceived) {
                log.warn("以下消息未收到反馈：{}", String.join(", ", missingMessages));
            }
            actualExpectedResult.put("compareFeedback", allReceived, String.join("\n", missingMessages));
            clearSerialRecvQueueManager();
            return actualExpectedResult;
        }
        if (message.contains("~")) {
            throw new IllegalArgumentException("接收消息格式错误: ~ 必须位于倒数第三位");
        }
        String expectedMessage = applyChecksumIfNecessary(message, checkType);
        String recvMessage = readPortFeedback(isHex, expectedMessage, Math.round(timeout * 1000));
        log.info("接收串口消息:{}", recvMessage);
        boolean pass = recvMessage != null && recvMessage.contains(expectedMessage);
        actualExpectedResult.put("compareFeedback", pass, recvMessage);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SERIAL_CHECK_MSG"})
    default ActualExpectedResult serialCheckMsg(String messageName) {
        String msgData = DeviceContextInfo.getInstance().getMessageDataMap().get(messageName);
        if (msgData == null) {
            return compareFeedback(msgData);
        } else {
            ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
            actualExpectedResult.put("compareFeedback", false, "未获取到需要对比的报文数据");
            return actualExpectedResult;
        }
    }


    /**
     * 串口发送数据并匹配反馈
     *
     * @param messageText 数据
     * @return 是否匹配
     * @throws OperationFailNotification
     */
    boolean sendAndMatch(MessageText messageText) throws OperationFailNotification;

    default boolean hasValidRangeFormat(String text) {
        if (text == null) return false;
        String cleanedMessage = text.replaceAll("\\s+$", ""); // 只去除末尾空格
        return cleanedMessage.length() >= 3 && cleanedMessage.charAt(cleanedMessage.length() - 3) == '~';
    }

    default RangeInfo parseRange(String text) {
        String[] parts = text.split("~");
        if (parts.length != 2) {
            throw new IllegalArgumentException("格式错误: 必须为 XXXXXXXX00~FF 格式");
        }

        String prefixWithStart = parts[0];
        String endStr = parts[1];

        if (prefixWithStart.length() < 2) {
            throw new IllegalArgumentException("前缀部分长度不足");
        }

        String prefix = prefixWithStart.substring(0, prefixWithStart.length() - 2);
        int start = Integer.parseInt(prefixWithStart.substring(prefixWithStart.length() - 2), 16);
        int end = Integer.parseInt(endStr, 16);

        if (start > end) {
            throw new IllegalArgumentException("起始值不能大于结束值");
        }

        return new RangeInfo(prefix, start, end);
    }

    default String getDynamicContext(List<String> contexts, int index) {
        return index < contexts.size() ? contexts.get(index) : null;
    }

    default String applyChecksumIfNecessary(String message, String checkType) {
        if (checkType == null) return message;

        switch (checkType.toUpperCase()) {
            case "ADD8":
                return ByteUtils.addChecksum(message, ByteUtils::calculateAdd8);
            case "XOR8":
                return ByteUtils.addChecksum(message, ByteUtils::calculateXor8);
            case "CRC8":
                return ByteUtils.addChecksum(message, ByteUtils::calculateCrc8);
            default:
                return message;
        }
    }

    class RangeInfo {
        final String prefix;
        final int start;
        final int end;

        RangeInfo(String prefix, int start, int end) {
            this.prefix = prefix;
            this.start = start;
            this.end = end;
        }
    }
}
