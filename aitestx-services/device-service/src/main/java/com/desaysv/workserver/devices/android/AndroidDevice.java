package com.desaysv.workserver.devices.android;

import com.desaysv.workserver.constants.AdbConstants;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.OperationParameterExtractException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.stream.GrabRequest;
import com.desaysv.workserver.utils.NetworkUtils;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Android设备
 */
@Slf4j
public abstract class AndroidDevice extends Device implements IAndroid {

    @Getter
    private final String deviceType = DeviceType.DEVICE_ANDROID;
    private final RestTemplate restTemplateClient = new RestTemplate();
    private Process videoStreamProcess; // 用于管理视频流进程

    // 视频流参数存储
    private volatile VideoStreamParameters currentVideoStreamParams;

    /**
     * 获取当前 scrcpy 可执行文件路径
     *
     * @return scrcpy.exe 的完整路径
     */
    @Getter
    private static String scrcpyPath = "D:\\uidp4666\\Downloads\\scrcpy-win64-v3.3.1\\scrcpy-win64-v3.3.1\\scrcpy.exe";


    public AndroidDevice() {
        this(new DeviceOperationParameter());
    }

    public AndroidDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        GrabRequest grabRequest = new GrabRequest();
        grabRequest.setDeviceUUID(getDeviceUniqueCode());
        grabRequest.setDeviceName(getDeviceName());
        grabRequest.setDeviceModel(getDeviceModel());
//        grabRequest.setDevicePort(getDevicePort());
        DeviceOperationParameter operationParameter = getDeviceOperationParameter();
        try {
            grabRequest.setWidth(operationParameter.getWidth());
            grabRequest.setHeight(operationParameter.getHeight());
        } catch (OperationParameterExtractException e) {
            log.warn("获取Android分辨率参数失败:{}", e.getMessage());
        }
        ParameterizedTypeReference<ResultEntity<Object>> typeRef = new ParameterizedTypeReference<ResultEntity<Object>>() {
        };
        String url = String.format("http://127.0.0.1:%d/AITestX/device/stream/grab", NetworkUtils.getServerPort());
        ResultEntity<Object> entity = restTemplateClient.exchange(url, HttpMethod.POST, new HttpEntity<>(grabRequest), typeRef).getBody();
//        throw new OperationFailException();
        if (entity == null) {
            throw new DeviceOpenException(String.format("Post请求失败:%s", url));
        }
        if (!entity.isOk()) {
            throw new DeviceOpenException(entity.getMessage());
        }
        return entity.isOk();
    }


    public static List<AdbStatus> getAllAndroids() {
        List<AdbStatus> adbStatusList = new ArrayList<>();
        try {
            List<String> adbDevicesResult = CommandUtils.executeCommandToArray(AdbConstants.Command.QUERY_ADB_DEVICES);
            log.info("adbDevicesResult:{}", adbDevicesResult);
            if (!adbDevicesResult.isEmpty()) {
                adbDevicesResult.remove(0);
            }
            for (String status : adbDevicesResult) {
                String[] statusArray = status.split("\t");
                AdbStatus adbStatus = new AdbStatus();
                adbStatus.setSerialNumber(statusArray[0]);
                adbStatus.setStatus(statusArray[1]);
                adbStatusList.add(adbStatus);
            }
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
        }
        return adbStatusList;
    }

    public String wrapAdbCommand(String command) {
        return command.replaceAll("adb", String.format("adb -s %s", getDeviceName()));
    }

    /**
     * 设置 scrcpy 可执行文件路径
     *
     * @param path scrcpy.exe 的完整路径
     */
    public static void setScrcpyPath(String path) {
        scrcpyPath = path;
        log.info("scrcpy路径已设置为: {}", path);
    }

    /**
     * 验证 scrcpy 是否可用
     *
     * @return 如果 scrcpy 可执行文件存在且可运行，则返回 true
     */
    public static boolean isScrcpyAvailable() {
        try {
            Process process = new ProcessBuilder(scrcpyPath, "--version").start();
            return process.waitFor(5, TimeUnit.SECONDS) && process.exitValue() == 0;
        } catch (Exception e) {
            log.warn("scrcpy验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有支持的视频编码格式
     *
     * @return 支持的编码格式列表
     */
    public static VideoCodec[] getSupportedVideoCodecs() {
        return VideoCodec.values();
    }

    /**
     * 根据编码格式名称获取 VideoCodec 枚举
     *
     * @param codecName 编码格式名称 (h264, h265, vp8, vp9)
     * @return 对应的 VideoCodec 枚举，如果未找到则返回 H264
     */
    public static VideoCodec getVideoCodecByName(String codecName) {
        if (codecName == null || codecName.trim().isEmpty()) {
            return VideoCodec.H264;
        }

        String normalizedName = codecName.toLowerCase().trim();
        for (VideoCodec codec : VideoCodec.values()) {
            if (codec.getCodecName().equals(normalizedName)) {
                return codec;
            }
        }

        log.warn("未找到编码格式: {}, 使用默认的 H264", codecName);
        return VideoCodec.H264;
    }

    /**
     * 诊断设备连接和scrcpy状态
     *
     * @return 诊断结果字符串
     */
    public String diagnoseDeviceAndScrcpy() {
        StringBuilder diagnosis = new StringBuilder();
        String deviceSerial = getDeviceName();

        diagnosis.append("=== 设备诊断报告 ===\n");
        diagnosis.append("设备序列号: ").append(deviceSerial).append("\n");

        // 检查设备连接
        try {
            List<AdbStatus> devices = getAllAndroids();
            boolean deviceFound = false;
            for (AdbStatus status : devices) {
                if (status.getSerialNumber().equals(deviceSerial)) {
                    diagnosis.append("设备状态: ").append(status.getStatus()).append("\n");
                    deviceFound = true;
                    break;
                }
            }
            if (!deviceFound) {
                diagnosis.append("设备状态: 未找到设备\n");
            }
        } catch (Exception e) {
            diagnosis.append("设备状态检查失败: ").append(e.getMessage()).append("\n");
        }

        // 检查scrcpy可用性
        diagnosis.append("scrcpy路径: ").append(scrcpyPath).append("\n");
        diagnosis.append("scrcpy可用: ").append(isScrcpyAvailable()).append("\n");

        // 检查视频流状态
        diagnosis.append("视频流运行中: ").append(isVideoStreamRunning()).append("\n");

        // 检查设备屏幕分辨率
        try {
            int[] screenSize = getDeviceScreenSize((UsbAndroid) this);
            diagnosis.append("屏幕分辨率: ").append(screenSize[0]).append("x").append(screenSize[1]).append("\n");
        } catch (Exception e) {
            diagnosis.append("屏幕分辨率获取失败: ").append(e.getMessage()).append("\n");
        }

        return diagnosis.toString();
    }

    public synchronized InputStream startVideoStream() throws IOException {
        return startVideoStream(0, 0, 0);
    }

    /**
     * 启动 Android 设备的视频流。
     *
     * @param width   视频宽度。如果为0或负数，则使用设备默认宽度。
     * @param height  视频高度。如果为0或负数，则使用设备默认高度。
     * @param bitRate 视频比特率 (例如 4000000 表示 4Mbps)。如果为0或负数，则使用默认比特率。
     * @return 视频流的 InputStream。
     * @throws IOException 如果执行 scrcpy 命令失败。
     */
    public synchronized InputStream startVideoStream(int width, int height, int bitRate) throws IOException {
        return startVideoStream(width, height, bitRate, VideoCodec.H264);
    }

    /**
     * 启动 Android 设备的视频流。
     *
     * @param width   视频宽度。如果为0或负数，则使用设备默认宽度。
     * @param height  视频高度。如果为0或负数，则使用设备默认高度。
     * @param bitRate 视频比特率 (例如 4000000 表示 4Mbps)。如果为0或负数，则使用默认比特率。
     * @param codec   视频编码格式
     * @return 视频流的 InputStream。
     * @throws IOException 如果执行 scrcpy 命令失败。
     */
    public synchronized InputStream startVideoStream(int width, int height, int bitRate, VideoCodec codec) throws IOException {
        if (isVideoStreamRunning()) {
            log.info("视频流已在运行，将先停止现有视频流。");
            stopVideoStream();
        }

        String deviceSerial = getDeviceName();
        if (deviceSerial == null || deviceSerial.isEmpty()) {
            throw new IOException("设备序列号/名称不可用。");
        }

        // 检查 scrcpy 是否可用
        if (!isScrcpyAvailable()) {
            throw new IOException("scrcpy 不可用，请检查路径配置: " + scrcpyPath);
        }

        // 如果宽度或高度为0，则自动获取设备屏幕分辨率
        if (width <= 0 || height <= 0) {
            try {
                log.info("正在获取设备 {} 的屏幕分辨率...", deviceSerial);
                int[] screenSize = getDeviceScreenSize((UsbAndroid) this);
                width = screenSize[0];
                height = screenSize[1];
                log.info("设备 {} 的屏幕分辨率: {}x{}", deviceSerial, width, height);
            } catch (Exception e) {
                log.warn("无法获取设备屏幕分辨率，使用默认值: {}x{}", width, height);
                if (width <= 0) width = 1920;
                if (height <= 0) height = 1080;
            }
        }

        // 设置默认比特率
        if (bitRate <= 0) {
            bitRate = 4000000; // 默认4Mbps
        }

        // 使用配置的 scrcpy 路径
        List<String> commandParts = new ArrayList<>();
        commandParts.add(scrcpyPath);
        commandParts.add("-s");
        commandParts.add(deviceSerial);

        // 基本配置
        commandParts.add("--no-audio"); // 禁用音频，只获取视频
        commandParts.add("--video-encoder=" + codec.getCodecName()); // 使用指定的视频编码器
        commandParts.add("--max-fps=30"); // 限制帧率为30fps
        commandParts.add("--no-playback"); // 禁用窗口显示，只输出视频流

        // 设置分辨率 - 使用宽度和高度中的较大值
        int maxSize = Math.max(width, height);
        commandParts.add("--max-size=" + maxSize);

        // 设置比特率
        commandParts.add("--video-bit-rate=" + bitRate);

        // 其他优化参数
        commandParts.add("--no-key-repeat"); // 禁用按键重复
        commandParts.add("--no-playback"); // 确保不显示窗口


        ProcessBuilder processBuilder = new ProcessBuilder(commandParts);
        processBuilder.redirectErrorStream(false); // 分离错误流以便调试

        log.info("执行scrcpy视频流命令: {}", String.join(" ", commandParts));
        try {
            videoStreamProcess = processBuilder.start();

            // 等待进程稳定启动，增加启动检查
            Thread.sleep(2000); // 增加等待时间到2秒

            // 检查进程是否成功启动
            if (!videoStreamProcess.isAlive()) {
                // 读取错误信息
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(videoStreamProcess.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                videoStreamProcess = null; // 重置，因为启动失败
                throw new IOException("启动scrcpy视频流进程失败。错误信息: " + errorOutput.toString().trim());
            }

            // 验证输入流是否可用
            InputStream inputStream = videoStreamProcess.getInputStream();
            if (inputStream == null) {
                throw new IOException("无法获取scrcpy视频流输入流");
            }

            // 等待一段时间让scrcpy初始化，然后检查是否有数据
            Thread.sleep(1000);
            if (inputStream.available() == 0) {
                log.warn("设备 {} scrcpy启动后暂无数据输出，可能正在初始化...", deviceSerial);
            }

            // 保存视频流参数
            currentVideoStreamParams = new VideoStreamParameters(width, height, bitRate, codec, deviceSerial);

            log.info("scrcpy视频流已成功启动。设备: {}, 分辨率: {}x{}, 最大尺寸: {}, 编码器: {}, 比特率: {}",
                    deviceSerial, width, height, maxSize, codec.getDisplayName(), bitRate);

            // 启动错误流监控线程
            startErrorStreamMonitor(deviceSerial);

            return inputStream;
        } catch (IOException e) {
            log.error("启动scrcpy视频流失败。设备: {}, 命令: {}. 错误: {}", deviceSerial, String.join(" ", commandParts), e.getMessage());
            videoStreamProcess = null; // 确保在失败时重置
            currentVideoStreamParams = null; // 重置参数
            throw e;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            currentVideoStreamParams = null; // 重置参数
            throw new IOException("启动scrcpy视频流时被中断", e);
        }
    }

    /**
     * 获取当前视频流参数
     *
     * @return 当前视频流参数，如果没有运行则返回null
     */
    public synchronized VideoStreamParameters getCurrentVideoStreamParameters() {
        return currentVideoStreamParams;
    }

    /**
     * 检查是否正在流式传输视频
     *
     * @return 如果正在流式传输视频则返回true
     */
    public synchronized boolean isStreamingVideo() {
        return isVideoStreamRunning() && currentVideoStreamParams != null;
    }

    /**
     * 启动错误流监控线程
     */
    private void startErrorStreamMonitor(String deviceSerial) {
        if (videoStreamProcess != null && videoStreamProcess.getErrorStream() != null) {
            Thread errorMonitor = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(videoStreamProcess.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        // 只记录重要的错误信息，避免日志过多
                        if (line.contains("error") || line.contains("Error") || line.contains("failed") || line.contains("Failed")) {
                            log.warn("设备 {} scrcpy错误: {}", deviceSerial, line);
                        } else {
                            log.debug("设备 {} scrcpy信息: {}", deviceSerial, line);
                        }
                    }
                } catch (IOException e) {
                    log.debug("设备 {} 错误流监控结束: {}", deviceSerial, e.getMessage());
                }
            });
            errorMonitor.setDaemon(true);
            errorMonitor.setName("ErrorMonitor-" + deviceSerial);
            errorMonitor.start();
        }
    }

    /**
     * 停止 Android 设备的视频流。
     */
    public synchronized void stopVideoStream() {
        if (videoStreamProcess != null && videoStreamProcess.isAlive()) {
            log.info("正在停止视频流... 设备: {}", getDeviceName());
            videoStreamProcess.destroyForcibly();
            try {
                if (!videoStreamProcess.waitFor(5, TimeUnit.SECONDS)) {
                    log.warn("视频流进程在5秒内未终止。设备: {}", getDeviceName());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待视频流进程终止时被中断。设备: {}", getDeviceName(), e);
            }
            log.info("视频流已停止。设备: {}", getDeviceName());
        }
        videoStreamProcess = null; // 清理引用
        currentVideoStreamParams = null; // 清理参数
    }

    /**
     * 检查视频流是否正在运行。
     *
     * @return 如果视频流进程存在且活动，则返回 true。
     */
    public synchronized boolean isVideoStreamRunning() {
        return videoStreamProcess != null && videoStreamProcess.isAlive();
    }

    /**
     * 获取当前 Android 设备 UI 层级结构的 XML 字符串。
     *
     * @return UI 层级结构的 XML 字符串。
     * @throws IOException 如果执行 ADB 命令失败或读取输出时发生错误。
     */
    public String getUIHierarchyXml() throws IOException {
        String deviceSerial = getDeviceName();
        if (deviceSerial == null || deviceSerial.isEmpty()) {
            throw new IOException("设备序列号/名称不可用。");
        }

        // 清理旧的 dump 文件 (可选, 但推荐)
        try {
            Process clearProcess = new ProcessBuilder("adb", "-s", deviceSerial, "shell", "rm", "/sdcard/window_dump.xml").start();
            if (!clearProcess.waitFor(5, TimeUnit.SECONDS)) {
                clearProcess.destroyForcibly();
                log.warn("清理 /sdcard/window_dump.xml 超时。");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("清理 /sdcard/window_dump.xml 时被中断。", e);
        } catch (IOException e) {
            // 忽略清理错误，可能文件不存在
            log.debug("清理 /sdcard/window_dump.xml 时出错 (可能文件不存在): {}", e.getMessage());
        }

        // 执行 uiautomator dump
        Process dumpProcess = null;
        try {
            dumpProcess = new ProcessBuilder("adb", "-s", deviceSerial, "shell", "uiautomator", "dump", "/sdcard/window_dump.xml").start();
            if (!dumpProcess.waitFor(15, TimeUnit.SECONDS)) { // UI dump 可能需要一些时间
                dumpProcess.destroyForcibly();
                throw new IOException("执行 uiautomator dump 超时。");
            }
            int exitCode = dumpProcess.exitValue();
            if (exitCode != 0) {
                // 读取错误流以获取更多信息
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(dumpProcess.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                throw new IOException("执行 uiautomator dump 失败，退出码: " + exitCode + ". 错误信息: " + errorOutput.toString().trim());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            dumpProcess.destroyForcibly();
            throw new IOException("执行 uiautomator dump 时被中断。", e);
        } catch (IOException e) {
            if (dumpProcess != null) {
                dumpProcess.destroyForcibly();
            }
            throw e; // 重新抛出原始的 IOException
        }


        // 读取 dump 的 XML 文件内容
        Process catProcess = null;
        StringBuilder xmlContent = new StringBuilder();
        try {
            catProcess = new ProcessBuilder("adb", "-s", deviceSerial, "shell", "cat", "/sdcard/window_dump.xml").start();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(catProcess.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    xmlContent.append(line).append(System.lineSeparator());
                }
            }

            if (!catProcess.waitFor(10, TimeUnit.SECONDS)) {
                catProcess.destroyForcibly();
                throw new IOException("读取 /sdcard/window_dump.xml 超时。");
            }
            int exitCode = catProcess.exitValue();
            if (exitCode != 0) {
                // 读取错误流以获取更多信息
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(catProcess.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                throw new IOException("读取 /sdcard/window_dump.xml 失败，退出码: " + exitCode + ". 错误信息: " + errorOutput.toString().trim());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            catProcess.destroyForcibly();
            throw new IOException("读取 /sdcard/window_dump.xml 时被中断。", e);
        } catch (IOException e) {
            if (catProcess != null) {
                catProcess.destroyForcibly();
            }
            throw e; // 重新抛出原始的 IOException
        }

        if (xmlContent.length() == 0) {
            log.warn("获取到的 UI 层级 XML 内容为空。设备: {}", deviceSerial);
            // 可以选择抛出异常或返回空字符串，具体取决于业务需求
            // throw new IOException("获取到的 UI 层级 XML 内容为空。");
        }

        return xmlContent.toString().trim();
    }

    @Override
    public RectSize getSize() {
        return null;
    }

    @Override
    public Frame grabFrame() {
        // 返回一个空的Frame，实际的视频捕获通过startVideoStream方法实现
        return new Frame();
    }

    /**
     * 通过 adb shell wm size 获取设备屏幕分辨率
     */
    private static int[] getDeviceScreenSize(UsbAndroid usbAndroid) throws IOException {
        String deviceSerial = usbAndroid.getDeviceName();
        if (deviceSerial == null || deviceSerial.isEmpty()) {
            throw new IOException("设备序列号/名称不可用。");
        }

        List<String> commandParts = new ArrayList<>();
        commandParts.add("adb");
        commandParts.add("-s");
        commandParts.add(deviceSerial);
        commandParts.add("shell");
        commandParts.add("wm");
        commandParts.add("size");

        ProcessBuilder processBuilder = new ProcessBuilder(commandParts);
        log.info("执行获取屏幕分辨率命令: {}", String.join(" ", commandParts));

        try {
            Process process = processBuilder.start();
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append(System.lineSeparator());
                }
            }

            if (!process.waitFor(10, TimeUnit.SECONDS)) {
                process.destroyForcibly();
                throw new IOException("获取屏幕分辨率超时。");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                throw new IOException("获取屏幕分辨率失败，退出码: " + exitCode + ". 错误信息: " + errorOutput.toString().trim());
            }

            // 解析输出，格式通常为 "Physical size: 1080x2340" 或 "Override size: 1080x2340"
            String outputStr = output.toString().trim();
            log.info("wm size 输出: {}", outputStr);

            // 查找包含分辨率信息的行
            String[] lines = outputStr.split(System.lineSeparator());
            for (String line : lines) {
                if (line.contains("x") && (line.contains("Physical size:") || line.contains("Override size:"))) {
                    // 提取分辨率部分，例如从 "Physical size: 1080x2340" 中提取 "1080x2340"
                    String resolution = line.substring(line.indexOf(":") + 1).trim();
                    String[] parts = resolution.split("x");
                    if (parts.length == 2) {
                        try {
                            int width = Integer.parseInt(parts[0].trim());
                            int height = Integer.parseInt(parts[1].trim());
                            return new int[]{width, height};
                        } catch (NumberFormatException e) {
                            log.warn("解析分辨率失败: {}", resolution);
                        }
                    }
                }
            }

            // 如果没有找到标准格式，尝试直接查找 "数字x数字" 模式
            for (String line : lines) {
                if (line.matches(".*\\d+x\\d+.*")) {
                    String[] parts = line.replaceAll("[^\\d+x\\d+]", "").split("x");
                    if (parts.length == 2) {
                        try {
                            int width = Integer.parseInt(parts[0].trim());
                            int height = Integer.parseInt(parts[1].trim());
                            return new int[]{width, height};
                        } catch (NumberFormatException e) {
                            log.warn("解析分辨率失败: {}", line);
                        }
                    }
                }
            }

            throw new IOException("无法从 wm size 输出中解析屏幕分辨率: " + outputStr);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("获取屏幕分辨率时被中断。", e);
        }
    }
}



