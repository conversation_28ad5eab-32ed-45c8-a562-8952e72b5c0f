package com.desaysv.workserver.devices.qnx;

import cn.hutool.core.util.StrUtil;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.devices.camera.base.IVisionDevice;
import com.desaysv.workserver.devices.camera.base.VisionRecognizeRequest;
import com.desaysv.workserver.entity.ConfigurableDevice;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.operation.command.ImageOperationCommand;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ExceptionUtils;
import com.desaysv.workserver.utils.SpringContextHolder;

public abstract class QnxDevice extends ConfigurableDevice<QnxDeviceConfig> implements IQnxDevice, IVisionDevice {

    public QnxDevice() {
        this(new DeviceOperationParameter());
    }

    public QnxDevice(DeviceOperationParameter deviceOperationParameter) {

    }

    @Override
    public VisionResult imageModelMatch(String templateName, boolean mustExist, float threshold, String matchText, String algorithm, float roiEnlargePercent, String model) {
        ImageOperationCommand imageOperationCommand = (ImageOperationCommand) SpringContextHolder.getBean(StrUtil.lowerFirst(ImageOperationCommand.class.getSimpleName()));
        VisionRecognizeRequest visionRecognizeRequest = new VisionRecognizeRequest();
        visionRecognizeRequest.setTemplateName(templateName);
        visionRecognizeRequest.setThreshold(threshold);
        visionRecognizeRequest.setAlgorithm(algorithm);
        visionRecognizeRequest.setMatchedText(matchText);
        visionRecognizeRequest.setMustExist(mustExist);
        visionRecognizeRequest.setColorMatchEnabled(true);
        visionRecognizeRequest.setProject(getDeviceOperationParameter().getProject());
        visionRecognizeRequest.setRoiEnlargePercent(roiEnlargePercent);
        VisionResult visionResult;
        try {
            visionResult = imageOperationCommand.getModelVisionResult(this, visionRecognizeRequest, model);
            return imageOperationCommand.handleModelVisionResult(visionResult, visionRecognizeRequest.getProject(), this);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            visionResult = new VisionResult();
            visionResult.setTemplateName(templateName);
            visionResult.setMessage(ExceptionUtils.getExceptionString(e));
            return visionResult;
        }
    }

    @Override
    public VisionResult imageMatch(String templateName, boolean mustExist, float threshold, String matchText, String algorithm, float roiEnlargePercent,
                                   float preWaitTime,
                                   float continuousCheckTimeout) {
        ImageOperationCommand imageOperationCommand = (ImageOperationCommand) SpringContextHolder.getBean(StrUtil.lowerFirst(ImageOperationCommand.class.getSimpleName()));
        VisionRecognizeRequest visionRecognizeRequest = new VisionRecognizeRequest();
        visionRecognizeRequest.setTemplateName(templateName);
        visionRecognizeRequest.setThreshold(threshold);
        visionRecognizeRequest.setAlgorithm(algorithm);
        //将实际字符替换成换行符号
        if (matchText != null) {
            matchText = matchText.replace("\\n", "\n");
        }
        visionRecognizeRequest.setTimeout(continuousCheckTimeout);
        visionRecognizeRequest.setPreWaitTime(preWaitTime);
        visionRecognizeRequest.setMatchedText(matchText);
        visionRecognizeRequest.setMustExist(mustExist);
        visionRecognizeRequest.setColorMatchEnabled(true);
        visionRecognizeRequest.setProject(getDeviceOperationParameter().getProject());
        visionRecognizeRequest.setRoiEnlargePercent(roiEnlargePercent);
        VisionResult visionResult;
        try {
            visionResult = imageOperationCommand.getVisionResult(this, visionRecognizeRequest);
            return imageOperationCommand.handleVisionResult(visionResult, visionRecognizeRequest.getProject(), this, false);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            visionResult = new VisionResult();
            visionResult.setTemplateName(templateName);
            visionResult.setMessage(ExceptionUtils.getExceptionString(e));
            return visionResult;
        }
    }

    @Override
    public VisionResult videoMatch(String templateName, double recognizedDuration, float threshold, float targetSimilarity, String algorithm, String matchText) {
        ImageOperationCommand imageOperationCommand = (ImageOperationCommand) SpringContextHolder.getBean(StrUtil.lowerFirst(ImageOperationCommand.class.getSimpleName()));
        VisionRecognizeRequest visionRecognizeRequest = new VisionRecognizeRequest();
        visionRecognizeRequest.setTemplateName(templateName);
        visionRecognizeRequest.setThreshold(threshold);
        visionRecognizeRequest.setTargetSimilarity(targetSimilarity);
        visionRecognizeRequest.setAlgorithm(algorithm == null ? "LightBlinking" : algorithm);
        visionRecognizeRequest.setMatchedText(matchText);
        visionRecognizeRequest.setRecognizedDuration(recognizedDuration);
        //visionRecognizeRequest.setMustExist(mustExist);
        visionRecognizeRequest.setColorMatchEnabled(true);
        visionRecognizeRequest.setVideoEnabled(true);
        visionRecognizeRequest.setProject(getDeviceOperationParameter().getProject());
        VisionResult visionResult;
        try {
            visionResult = imageOperationCommand.getVisionResult(this, visionRecognizeRequest);
            return visionResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            visionResult = new VisionResult();
            visionResult.setTemplateName(templateName);
            visionResult.setMessage(ExceptionUtils.getExceptionString(e));
            return visionResult;
        }
    }


    @Override
    public OperationResult takePhoto() {
        ImageOperationCommand imageOperationCommand = (ImageOperationCommand) SpringContextHolder.getBean(StrUtil.lowerFirst(ImageOperationCommand.class.getSimpleName()));
        ImageOperationCommand.ImageOperationContext imageOperationContext = new ImageOperationCommand.ImageOperationContext();
        imageOperationContext.setDevice(this);
        imageOperationContext.setOperationResult(new OperationResult());
        imageOperationContext.setProjectName(getDeviceOperationParameter().getProject());
        return imageOperationCommand.cameraScreenShoot(imageOperationContext);
    }

    @Override
    public OperationResult videoRecording(String status) {
        ImageOperationCommand imageOperationCommand = (ImageOperationCommand) SpringContextHolder.getBean(StrUtil.lowerFirst(ImageOperationCommand.class.getSimpleName()));
        ImageOperationCommand.ImageOperationContext imageOperationContext = new ImageOperationCommand.ImageOperationContext();
        imageOperationContext.setDevice(this);
        imageOperationContext.setOperationResult(new OperationResult());
        imageOperationContext.setProjectName(getDeviceOperationParameter().getProject());
        if (status.equalsIgnoreCase("start")) {
            return imageOperationCommand.startRecording(imageOperationContext);
        } else if (status.equalsIgnoreCase("stop")) {
            return imageOperationCommand.stopRecording(imageOperationContext);
        } else {
            return new OperationResult().fail("录像操作失败");
        }
    }
}
