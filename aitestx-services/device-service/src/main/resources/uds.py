import os
import ctypes
import sys
from ctypes import wintypes


# 定义 VKeyGenResultEx 枚举值
class VKeyGenResultEx:
    KGRE_Ok = 0
    KGRE_BufferToSmall = 1
    KGRE_SecurityLevelInvalid = 2
    KGRE_VariantInvalid = 3
    KGRE_UnspecifiedError = 4
    KGRE_Error = -1


# 错误码到描述的映射
ERROR_CODE_MAPPING = {
    VKeyGenResultEx.KGRE_Ok: "操作成功",
    VKeyGenResultEx.KGRE_BufferToSmall: "缓冲区太小",
    VKeyGenResultEx.KGRE_SecurityLevelInvalid: "安全级别无效",
    VKeyGenResultEx.KGRE_VariantInvalid: "变体无效",
    VKeyGenResultEx.KGRE_UnspecifiedError: "未指定错误",
    VKeyGenResultEx.KGRE_Error: "通用错误"
}


def print_buffer(buffer, size, label="Buffer"):
    """格式化打印缓冲区内容"""
    if size == 0:
        print(f"{label}: (空)")
        return

    # 每行显示16个字节
    bytes_per_line = 16
    lines = (size + bytes_per_line - 1) // bytes_per_line

    print(f"{label} ({size} bytes):")
    for i in range(lines):
        start = i * bytes_per_line
        end = min(start + bytes_per_line, size)

        # 字节的十六进制表示（转换为无符号）
        hex_part = " ".join([f"{b & 0xFF:02X}" for b in buffer[start:end]])
        hex_part = hex_part.ljust(bytes_per_line * 3)  # 对齐

        # 字节的 ASCII 表示（转换为无符号）
        ascii_part = "".join([chr(b & 0xFF) if 32 <= (b & 0xFF) <= 126 else '.' for b in buffer[start:end]])

        print(f"  {start:04X}: {hex_part}  | {ascii_part} |")


def generate_key_ex(seed: bytes, security_level: int, variant: str,
                    dll_path: str, debug: bool = False) -> tuple[bool, bytes, str]:
    """
    通过 UDS 的 seedkey.dll 中的 GenerateKeyEx 方法生成 key

    参数:
        seed: 从诊断设备获取的 seed 字节数据
        security_level: 安全级别
        variant: 变体字符串
        dll_path: DLL 文件的路径
        debug: 是否启用调试模式

    返回:
        元组 (成功标志, 生成的 key 字节数据, 错误消息)
    """
    try:
        # 检查 DLL 文件是否存在
        if not os.path.exists(dll_path):
            raise FileNotFoundError(f"DLL 文件不存在: {dll_path}")

        # 加载 DLL
        if debug:
            print(f"正在加载 DLL: {dll_path}")

        seedkey_dll = ctypes.WinDLL(dll_path)

        # 获取 GenerateKeyEx 函数
        generate_key_ex_func = seedkey_dll.GenerateKeyEx

        # 设置函数参数和返回类型
        if debug:
            print("设置函数参数和返回类型...")

        generate_key_ex_func.argtypes = [
            ctypes.POINTER(ctypes.c_byte),  # ipSeedArray
            ctypes.c_int,  # iSeedArraySize
            ctypes.c_int,  # iSecurityLevel
            ctypes.c_wchar_p,  # ipVariant
            ctypes.POINTER(ctypes.c_byte),  # iopKeyArray
            ctypes.c_int,  # iMaxKeyArraySize
            ctypes.POINTER(ctypes.c_int)  # oActualKeyArraySize
        ]
        generate_key_ex_func.restype = ctypes.c_int  # 返回枚举值（int类型）

        # 准备参数
        if debug:
            print("\n===== 输入参数 =====")
            print(f"Seed: {seed.hex().upper()} ({len(seed)} bytes)")
            print(f"Security Level: {security_level}")
            print(f"Variant: {variant}")

        seed_array = (ctypes.c_byte * len(seed))(*seed)
        seed_size = len(seed)

        # 最大 key 数组大小
        max_key_size = 256
        key_array = (ctypes.c_byte * max_key_size)()

        # 实际 key 大小
        actual_key_size = ctypes.c_int(0)

        # 调用函数
        if debug:
            print("\n调用 GenerateKeyEx 函数...")

        result_code = generate_key_ex_func(
            seed_array, seed_size,
            security_level, variant,
            key_array, max_key_size,
            ctypes.byref(actual_key_size)
        )

        # 提取结果
        actual_size = actual_key_size.value

        # 将有符号字节转换为无符号字节
        key_data = bytes(b & 0xFF for b in key_array[:actual_size])

        # 检查结果
        success = (result_code == VKeyGenResultEx.KGRE_Ok)
        error_message = ERROR_CODE_MAPPING.get(result_code, f"未知错误码: {result_code}")

        if debug:
            print("\n===== 输出参数 =====")
            print(f"返回码: {result_code} ({error_message})")
            print(f"实际 Key 大小: {actual_size} 字节")

            # 打印 seed 缓冲区
            print_buffer(seed_array, seed_size, "输入 Seed 缓冲区")

            # 打印 key 缓冲区
            print_buffer(key_array, actual_size, "输出 Key 缓冲区")

            if success:
                print(f"\n生成的 Key: {key_data.hex().upper()}")

        return success, key_data, error_message

    except Exception as e:
        print(f"生成 key 时出错: {e}")
        return False, b"", str(e)


# 在 uds.py 文件末尾添加以下代码
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Generate key via GenerateKeyEx")
    parser.add_argument("--seed", required=True, help="Seed in hex format")
    parser.add_argument("--security-level", type=int, required=True, help="Security level")
    parser.add_argument("--variant", required=True, help="Variant string")
    parser.add_argument("--dll-path", default="D:\\UID01728\\Downloads\\Proton_IBCM.dll", help="DLL path")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")

    args = parser.parse_args()

    # 将种子从十六进制转换为字节
    seed_bytes = bytes.fromhex(args.seed)

    # 调用 generate_key_ex 函数
    success, key_data, error_message = generate_key_ex(
        seed=seed_bytes,
        security_level=args.security_level,
        variant=args.variant,
        dll_path=args.dll_path,
        debug=args.debug
    )

    if success:
        # 输出生成的密钥（十六进制格式）
        print(key_data.hex().upper())
    else:
        # 输出错误信息到标准错误流
        print(f"Error: {error_message}", file=sys.stderr)
        sys.exit(1)
